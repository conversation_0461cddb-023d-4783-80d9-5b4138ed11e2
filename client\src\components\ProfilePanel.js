import { useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

export default function ProfilePanel({ isOpen, onClose }) {
  const profilePanelRef = useRef(null);

  // Close profile panel when clicking outside
  useEffect(() => {
    if (!isOpen) return;
    
    const handleClickOutside = (event) => {
      if (profilePanelRef.current && !profilePanelRef.current.contains(event.target) && 
          !event.target.closest('.profile-icon')) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  const profilePanelVariants = {
    hidden: { 
      x: '100%',
      opacity: 0,
    },
    visible: { 
      x: '0%',
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30,
        duration: 0.3
      }
    },
    exit: {
      x: '100%',
      opacity: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30,
        duration: 0.2
      }
    }
  };

  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { duration: 0.3 }
    },
    exit: { 
      opacity: 0,
      transition: { duration: 0.2 }
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop overlay */}
          <motion.div
            className="fixed inset-0 font-[body] bg-black bg-opacity-50 z-20"
            variants={backdropVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            onClick={onClose}
          />
          
          {/* Side Panel */}
          <motion.div
            ref={profilePanelRef}
            className="fixed top-0 right-0 h-full w-full sm:w-80 bg-white shadow-xl z-30 flex flex-col"
            variants={profilePanelVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            {/* Panel Header */}
            <div className="p-6 border-b border-gray-100 flex justify-between items-center">
              <h2 className="text-xl font-bold">Profile</h2>
              <button 
                onClick={onClose}
                className="p-2 rounded-full hover:bg-gray-100 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
            
            {/* User Profile */}
            <div className="p-6 flex items-center space-x-4 border-b border-gray-100">
              <div className="w-16 h-16 rounded-full bg-black text-white flex items-center justify-center text-xl font-medium">
                JD
              </div>
              <div>
                <h3 className="text-lg font-medium">John Doe</h3>
                <p className="text-gray-500"><EMAIL></p>
                <div className="flex items-center space-x-1 mt-1">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                  <span className="text-sm">4.9</span>
                </div>
              </div>
            </div>
            
            {/* Navigation Menu */}
            <div className="flex-grow overflow-y-auto">
              <motion.div 
                className="p-6 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors"
                whileHover={{ x: 5 }}
              >
                <div className="flex items-center space-x-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clipRule="evenodd" />
                  </svg>
                  <div>
                    <h3 className="text-base font-medium">Edit Profile</h3>
                    <p className="text-sm text-gray-500">Update your information</p>
                  </div>
                </div>
              </motion.div>
              
              <motion.div 
                className="p-6 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors"
                whileHover={{ x: 5 }}
              >
                <div className="flex items-center space-x-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z" />
                    <path fillRule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clipRule="evenodd" />
                  </svg>
                  <div>
                    <h3 className="text-base font-medium">Payment Methods</h3>
                    <p className="text-sm text-gray-500">Manage your cards</p>
                  </div>
                </div>
              </motion.div>
              
              <div className="p-6 border-b border-gray-100">
                <h3 className="text-base font-medium mb-4">Past Rides</h3>
                <div className="space-y-4">
                  {[
                    { date: 'July 24, 2023', from: 'Downtown', to: 'Airport', price: '$24.50' },
                    { date: 'July 18, 2023', from: 'Mall', to: 'Home', price: '$18.75' }
                  ].map((ride, index) => (
                    <motion.div 
                      key={index} 
                      className="bg-gray-50 p-3 rounded-lg hover:bg-gray-100 cursor-pointer"
                      whileHover={{ scale: 1.02 }}
                    >
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">{ride.date}</span>
                        <span className="font-medium">{ride.price}</span>
                      </div>
                      <div className="text-sm mt-1">
                        <span>{ride.from}</span>
                        <span className="mx-2">→</span>
                        <span>{ride.to}</span>
                      </div>
                    </motion.div>
                  ))}
                  
                  <motion.button 
                    className="text-sm text-center w-full py-2 hover:underline text-gray-600"
                    whileHover={{ scale: 1.05 }}
                  >
                    View All Rides
                  </motion.button>
                </div>
              </div>
              
              <div className="p-6 border-b border-gray-100">
                <h3 className="text-base font-medium mb-4">Rewards</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium">RidePoints</span>
                    <span className="text-lg font-bold">1,250</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div className="bg-black h-2.5 rounded-full" style={{ width: '60%' }}></div>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">750 more points until free ride</p>
                </div>
              </div>
            </div>
            
            {/* Footer Options */}
            <div className="mt-auto border-t border-gray-100">
              <motion.div 
                className="p-6 cursor-pointer hover:bg-gray-50 transition-colors flex items-center space-x-4"
                whileHover={{ x: 5 }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Help & Support</span>
              </motion.div>
              
              <motion.div 
                className="p-6 cursor-pointer hover:bg-gray-50 transition-colors flex items-center space-x-4"
                whileHover={{ x: 5 }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                <span>Log Out</span>
              </motion.div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
