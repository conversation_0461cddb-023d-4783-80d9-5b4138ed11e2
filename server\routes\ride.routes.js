const express = require('express');
const router = express.Router();
const { body, param, query } = require('express-validator');
const rideController = require('../controllers/ride.controller');
const authMiddleware = require('../middlewares/auth.middleware');

// Create a new ride request (User only)
router.post('/create', [
  body('pickup.address').notEmpty().withMessage('Pickup address is required'),
  body('destination.address').notEmpty().withMessage('Destination address is required'),
  body('vehicleType').isIn(['sedan', 'suv', 'van', 'car', 'motorcycle', 'bicycle', 'auto']).withMessage('Invalid vehicle type'),
  body('pickup.coordinates.lat').optional().isFloat({ min: -90, max: 90 }).withMessage('Valid pickup latitude required'),
  body('pickup.coordinates.lng').optional().isFloat({ min: -180, max: 180 }).withMessage('Valid pickup longitude required'),
  body('destination.coordinates.lat').optional().isFloat({ min: -90, max: 90 }).withMessage('Valid destination latitude required'),
  body('destination.coordinates.lng').optional().isFloat({ min: -180, max: 180 }).withMessage('Valid destination longitude required')
], authMiddleware.authUser, rideController.createRide);

// Find nearby captains for a ride (User only)
router.get('/:rideId/nearby-captains', [
  param('rideId').isMongoId().withMessage('Valid ride ID is required'),
  query('radius').optional().isFloat({ min: 0.1, max: 50 }).withMessage('Radius must be between 0.1 and 50 km')
], authMiddleware.authUser, rideController.findNearbyCaptains);

// Accept a ride (Captain only)
router.post('/:rideId/accept', [
  param('rideId').isMongoId().withMessage('Valid ride ID is required')
], authMiddleware.authCaptain, rideController.acceptRide);

// Start a ride (Captain only)
router.post('/:rideId/start', [
  param('rideId').isMongoId().withMessage('Valid ride ID is required'),
  body('otp').isLength({ min: 4, max: 4 }).withMessage('Valid 4-digit OTP is required')
], authMiddleware.authCaptain, rideController.startRide);

// Complete a ride (Captain only)
router.post('/:rideId/complete', [
  param('rideId').isMongoId().withMessage('Valid ride ID is required')
], authMiddleware.authCaptain, rideController.completeRide);

// Cancel a ride (User or Captain)
router.post('/:rideId/cancel', [
  param('rideId').isMongoId().withMessage('Valid ride ID is required'),
  body('reason').optional().isString().withMessage('Cancellation reason must be a string')
], (req, res, next) => {
  // Allow both user and captain to cancel
  if (req.headers.authorization) {
    // Try user auth first
    authMiddleware.authUser(req, res, (err) => {
      if (err || !req.user) {
        // If user auth fails, try captain auth
        authMiddleware.authCaptain(req, res, next);
      } else {
        next();
      }
    });
  } else {
    return res.status(401).json({ message: 'Authorization required' });
  }
}, rideController.cancelRide);

// Get ride details (User or Captain)
router.get('/:rideId', [
  param('rideId').isMongoId().withMessage('Valid ride ID is required')
], (req, res, next) => {
  // Allow both user and captain to view ride details
  if (req.headers.authorization) {
    // Try user auth first
    authMiddleware.authUser(req, res, (err) => {
      if (err || !req.user) {
        // If user auth fails, try captain auth
        authMiddleware.authCaptain(req, res, next);
      } else {
        next();
      }
    });
  } else {
    return res.status(401).json({ message: 'Authorization required' });
  }
}, rideController.getRideDetails);

module.exports = router;
