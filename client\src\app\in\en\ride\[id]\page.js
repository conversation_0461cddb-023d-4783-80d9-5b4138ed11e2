"use client";

import { useState, useRef, useEffect, use } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Header from '@/components/Header';
import ProfilePanel from '@/components/ProfilePanel';
import MapView from '@/components/MapView';
import RidePanel from '@/components/RidePanel';

export default function RideRequestPage({ params }) {
  // Unwrap the params Promise
  const unwrappedParams = use(params);
  const rideId = unwrappedParams.id;
  
  const [isProfilePanelOpen, setIsProfilePanelOpen] = useState(false);
  const [isPanelExpanded, setIsPanelExpanded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isVehicleSelectionMode, setIsVehicleSelectionMode] = useState(false);
  
  // Location state (lifted up to share between components)
  const [location, setLocation] = useState('');
  const [destination, setDestination] = useState('');

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkIfMobile();
    window.addEventListener('resize', checkIfMobile);
    
    return () => {
      window.removeEventListener('resize', checkIfMobile);
    };
  }, []);

  return (
    <div className="h-screen font-[body] flex flex-col overflow-hidden text-black bg-white">
      {/* Header */}
      <Header 
        onProfileClick={() => setIsProfilePanelOpen(true)} 
      />

      {/* Profile Side Panel */}
      <ProfilePanel 
        isOpen={isProfilePanelOpen}
        onClose={() => setIsProfilePanelOpen(false)}
      />

      {/* Main Content: Map and Form */}
      <div className="flex-grow flex flex-col md:flex-row relative">
        {/* Map */}
        <MapView 
          isMobile={isMobile} 
          location={location}
          destination={destination}
          isVehicleSelectionMode={isVehicleSelectionMode}
        />

        {/* Ride Panel (Location inputs or Vehicle selection) */}
        <RidePanel 
          isMobile={isMobile}
          isPanelExpanded={isPanelExpanded}
          setIsPanelExpanded={setIsPanelExpanded}
          location={location}
          setLocation={setLocation}
          destination={destination}
          setDestination={setDestination}
          isVehicleSelectionMode={isVehicleSelectionMode}
          setIsVehicleSelectionMode={setIsVehicleSelectionMode}
          rideId={rideId}
        />
      </div>
    </div>
  );
}
