import { motion } from 'framer-motion';
import { useState } from 'react';

export default function VehicleSelection({ selectedVehicleType, setSelectedVehicleType, onConfirmRide }) {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null);
  
  const vehicleTypes = [
    {
      id: 'sedan',
      name: '<PERSON><PERSON>',
      price: '$14.50',
      time: '3 min away',
      available: true,
      capacity: '4 people',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      )
    },
    {
      id: 'suv',
      name: 'SUV',
      price: '$18.75',
      time: '5 min away',
      available: true,
      capacity: '6 people',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
        </svg>
      )
    },
    {
      id: 'van',
      name: 'Van',
      price: '$22.30',
      time: '10 min away',
      available: false,
      capacity: '8 people',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      )
    },
    {
      id: 'car',
      name: 'Car',
      price: '$13.25',
      time: '2 min away',
      available: true,
      capacity: '4 people',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
        </svg>
      )
    },
    {
      id: 'motorcycle',
      name: 'Motorcycle',
      price: '$10.80',
      time: '1 min away',
      available: true,
      capacity: '1 person',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
        </svg>
      )
    },
    {
      id: 'bicycle',
      name: 'Bicycle',
      price: '$8.50',
      time: '4 min away',
      available: true,
      capacity: '1 person',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
        </svg>
      )
    },
    {
      id: 'auto',
      name: 'Auto',
      price: '$9.75',
      time: '6 min away',
      available: false,
      capacity: '3 people',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    }
  ];

  const paymentMethods = [
    {
      id: 'cash',
      name: 'Cash',
      description: 'Pay with cash to driver',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2z" />
        </svg>
      )
    },
    {
      id: 'online',
      name: 'Online Payment',
      description: 'Cards, UPI, Wallets',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
        </svg>
      )
    }
  ];

  const cardVariants = {
    unselected: { 
      scale: 0.95,
      opacity: 0.7,
      border: "2px solid rgba(229, 231, 235, 1)"
    },
    selected: { 
      scale: 1, 
      opacity: 1, 
      border: "2px solid rgba(0, 0, 0, 1)",
      transition: { type: "spring", stiffness: 400, damping: 10 }
    },
    unavailable: {
      scale: 0.95,
      opacity: 0.5,
      border: "2px solid rgba(229, 231, 235, 0.5)"
    }
  };

  const paymentVariants = {
    unselected: { 
      scale: 0.98,
      opacity: 0.7,
      backgroundColor: "#f9fafb",
      transition: { duration: 0.2 }
    },
    selected: { 
      scale: 1, 
      opacity: 1,
      backgroundColor: "#f3f4f6",
      transition: { type: "spring", stiffness: 400, damping: 10 }
    }
  };

  const handleConfirm = () => {
    onConfirmRide(selectedPaymentMethod);
  };

  return (
    <div className="space-y-6 font-[body]">
      <div className="grid grid-cols-1 gap-4">
        {vehicleTypes.map((vehicle) => (
          <motion.div
            key={vehicle.id}
            className={`bg-white border-2 rounded-xl p-4 relative overflow-y-scroll ${!vehicle.available ? 'cursor-not-allowed' : 'cursor-pointer'}`}
            variants={cardVariants}
            animate={!vehicle.available ? "unavailable" : selectedVehicleType === vehicle.id ? "selected" : "unselected"}
            whileHover={vehicle.available ? { scale: 1.02 } : {}}
            onClick={() => vehicle.available && setSelectedVehicleType(vehicle.id)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className={`p-2 ${vehicle.available ? 'bg-gray-100' : 'bg-gray-100 opacity-50'} rounded-full mr-4`}>
                  {vehicle.icon}
                </div>
                <div>
                  <div className="flex items-center">
                    <h3 className="font-medium">{vehicle.name}</h3>
                    {vehicle.available ? (
                      <span className="ml-2 text-xs bg-green-100 text-green-800 py-0.5 px-2 rounded-full">Available</span>
                    ) : (
                      <span className="ml-2 text-xs bg-red-100 text-red-800 py-0.5 px-2 rounded-full">Unavailable</span>
                    )}
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    {vehicle.available ? (
                      <>{vehicle.time} • {vehicle.capacity}</>
                    ) : (
                      <span>No drivers nearby</span>
                    )}
                  </div>
                </div>
              </div>
              <span className="font-bold">{vehicle.price}</span>
            </div>

            {/* Vehicle image */}
            <div className="mt-2 flex justify-end">
              <motion.div 
                className={`h-12 w-24 ${vehicle.available ? 'bg-gray-200' : 'bg-gray-100'} rounded-md relative`}
                animate={{ x: selectedVehicleType === vehicle.id ? [20, 0] : 0 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
              >
                <div className="absolute inset-0 flex items-center justify-center">
                  {!vehicle.available ? (
                    <span className="text-xs text-gray-400">Not Available</span>
                  ) : (
                    <span className="text-xs text-gray-500">{vehicle.id.charAt(0).toUpperCase() + vehicle.id.slice(1)}</span>
                  )}
                </div>
              </motion.div>
            </div>

            {/* Selection indicator */}
            {vehicle.available && selectedVehicleType === vehicle.id && (
              <motion.div 
                className="absolute top-3 right-4 h-5 w-5 bg-black rounded-full flex items-center justify-center"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 500, damping: 15 }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-white" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </motion.div>
            )}
          </motion.div>
        ))}
      </div>

      {/* Payment Selection */}
      {selectedVehicleType && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          transition={{ duration: 0.3 }}
          className="mt-6 border-t border-gray-200 pt-6"
        >
          <h3 className="text-base font-medium mb-3">Select Payment Method</h3>
          <div className="grid grid-cols-2 gap-3">
            {paymentMethods.map((method) => (
              <motion.div 
                key={method.id}
                className={`p-4 rounded-lg cursor-pointer ${selectedPaymentMethod === method.id ? 'border-2 border-black' : 'border border-gray-200'}`}
                variants={paymentVariants}
                animate={selectedPaymentMethod === method.id ? "selected" : "unselected"}
                whileHover={{ scale: 1.02 }}
                onClick={() => setSelectedPaymentMethod(method.id)}
              >
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-gray-100 rounded-full">
                    {method.icon}
                  </div>
                  <div>
                    <h4 className="font-medium">{method.name}</h4>
                    <p className="text-xs text-gray-500">{method.description}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      <motion.div 
        className="pt-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <motion.button
          type="button"
          disabled={!selectedVehicleType || !selectedPaymentMethod}
          onClick={handleConfirm}
          className={`w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-lg font-medium text-white 
            ${(!selectedVehicleType || !selectedPaymentMethod) ? 'bg-gray-300' : 'bg-black hover:bg-gray-800'} 
            focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200`}
          whileHover={(selectedVehicleType && selectedPaymentMethod) ? { scale: 1.02 } : {}}
          whileTap={(selectedVehicleType && selectedPaymentMethod) ? { scale: 0.98 } : {}}
        >
          Confirm {selectedVehicleType?.charAt(0).toUpperCase() + selectedVehicleType?.slice(1) || "Ride"}
        </motion.button>
      </motion.div>
      
      <div className="text-xs text-gray-500 text-center mt-2">
        By confirming, you agree to our <span className="underline cursor-pointer">Terms of Service</span>
      </div>
    </div>
  );
}
