'use client';

import { useState } from 'react';
import Link from 'next/link';
import AuthLayout from '@/components/auth/AuthLayout';
import RegisterForm from '@/components/auth/RegisterForm';
import SocialLogin from '@/components/auth/SocialLogin';
import ErrorMessage from '@/components/auth/ErrorMessage';
import { useAuth } from '@/context/AuthContext';

export default function RegisterPage() {
  const [localError, setLocalError] = useState('');
  const { registerUserAccount, loading, error } = useAuth();

  const handleRegister = async (userData) => {
    setLocalError('');
    
    if (userData.password !== document.getElementById('confirmPassword').value) {
      setLocalError('Passwords do not match');
      return;
    }
    
    try {
      // Use the registerUserAccount function from AuthContext
      const result = await registerUserAccount(userData);
      
      // If there was an error during registration, it will be handled by the context
      if (!result.success) {
        setLocalError(result.error);
      }
      
      // No need for redirection here as it's handled in the context
    } catch (err) {
      setLocalError('Registration failed. Please try again.');
      console.error(err);
    }
  };

  const handleGoogleRegister = async () => {
    // Google registration would be handled by a similar function in the context
    // For now, just show a message
    setLocalError('Google registration is not yet implemented.');
  };

  const loginLink = (
    <>
      Already have an account?{' '}
      <Link href="/user/login" className="font-medium text-black hover:text-gray-800">
        Log in
      </Link>
    </>
  );

  return (
    <AuthLayout 
      title="Create your account" 
      subtitle="Join RideUp today"
      footer={loginLink}
    >
      <ErrorMessage message={localError || error} />
      
      <RegisterForm onSubmit={handleRegister} loading={loading} />
      
      <div className="mt-6">
        <SocialLogin onGoogleLogin={handleGoogleRegister} loading={loading} />
      </div>
    </AuthLayout>
  );
}
