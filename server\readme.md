# RideUp API Documentation

## User Endpoints

### Register User
**Endpoint:** `POST /users/register`

**Description:**  
Creates a new user account in the system.

**Request Body:**
```json
{
  "fullName": {
    "firstName": "string",
    "lastName": "string"
  },
  "email": "string",
  "password": "string"
}
```

**Validation Rules:**
- `fullName.firstName`: Must be at least 3 characters
- `fullName.lastName`: Must be at least 3 characters
- `email`: Must be a valid email format
- `password`: Must be at least 8 characters

**Responses:**

| Status Code | Description |
|-------------|-------------|
| 201         | User successfully created |
| 400         | Validation error |

**Success Response Example:**
```json
{
  "token": "jwt-token",
  "user": {
    "fullName": {
      "firstName": "John",
      "lastName": "Doe"
    },
    "email": "<EMAIL>",
    "_id": "user-id",
    "socketId": null
  }
}
```

**Error Response Example:**
```json
{
  "errors": [
    {
      "msg": "Email is not valid",
      "param": "email",
      "location": "body"
    }
  ]
}
```

### Login User
**Endpoint:** `POST /users/login`

**Description:**  
Authenticates a user and returns a JWT token.

**Request Body:**
```json
{
  "email": "string",
  "password": "string"
}
```

**Validation Rules:**
- `email`: Must be a valid email format
- `password`: Must be at least 5 characters

**Responses:**

| Status Code | Description |
|-------------|-------------|
| 200         | Login successful |
| 400         | Invalid credentials or validation error |

**Success Response Example:**
```json
{
  "token": "jwt-token",
  "user": {
    "fullName": {
      "firstName": "John",
      "lastName": "Doe"
    },
    "email": "<EMAIL>",
    "_id": "user-id",
    "socketId": null
  }
}
```

**Error Response Example:**
```json
{
  "error": "Invalid email or password"
}
```

**Validation Error Example:**
```json
{
  "errors": [
    {
      "msg": "Email is not valid",
      "param": "email",
      "location": "body"
    }
  ]
}
```

### Get User Profile
**Endpoint:** `GET /users/profile`

**Description:**  
Retrieves the authenticated user's profile information.

**Authentication:**  
Requires a valid JWT token in the Authorization header.

**Request Body:**
None

**Responses:**

| Status Code | Description |
|-------------|-------------|
| 200         | Success |
| 401         | Unauthorized - Invalid or missing token |

**Success Response Example:**
```json
{
  "user": {
    "fullName": {
      "firstName": "John",
      "lastName": "Doe"
    },
    "email": "<EMAIL>",
    "_id": "user-id",
    "socketId": null
  }
}
```

**Error Response Example:**
```json
{
  "error": "Unauthorized - Please log in"
}
```

### Logout User
**Endpoint:** `GET /users/logout`

**Description:**  
Logs out the currently authenticated user.

**Authentication:**  
Requires a valid JWT token in the Authorization header.

**Request Body:**
None

**Responses:**

| Status Code | Description |
|-------------|-------------|
| 200         | Logout successful |
| 401         | Unauthorized - Invalid or missing token |

**Success Response Example:**
```json
{
  "message": "Logged out successfully"
}
```

**Error Response Example:**
```json
{
  "error": "Unauthorized - Please log in"
}
```

## Captain Endpoints

### Register Captain
**Endpoint:** `POST /captains/register`

**Description:**  
Creates a new captain account in the system.

**Request Body:**
```json
{
  "fullName": {
    "firstName": "string",
    "lastName": "string"
  },
  "email": "string",
  "password": "string",
  "phoneNumber": "string",
  "vehicle": {
    "color": "string",
    "plateNumber": "string",
    "capacity": "number",
    "vehicleType": "string"
  }
}
```

**Validation Rules:**
- `fullName.firstName`: Must be at least 2 characters
- `email`: Must be a valid email format
- `password`: Must be at least 5 characters
- `phoneNumber`: Must not be empty
- `vehicle.color`: Must not be empty
- `vehicle.plateNumber`: Must not be empty
- `vehicle.capacity`: Must not be empty
- `vehicle.vehicleType`: Must not be empty

**Responses:**

| Status Code | Description |
|-------------|-------------|
| 201         | Captain successfully created |
| 400         | Validation error or captain already exists |

**Success Response Example:**
```json
{
  "token": "jwt-token",
  "captain": {
    "fullName": {
      "firstName": "John",
      "lastName": "Doe"
    },
    "email": "<EMAIL>",
    "_id": "captain-id",
    "vehicle": {
      "color": "Black",
      "plateNumber": "ABC123",
      "capacity": 4,
      "vehicleType": "sedan"
    }
  }
}
```

**Error Response Example:**
```json
{
  "errors": [
    {
      "msg": "Email is invalid",
      "param": "email",
      "location": "body"
    }
  ]
}
```

**Captain Already Exists Error:**
```json
{
  "message": "Captain already exist"
}
```

### Login Captain
**Endpoint:** `POST /captains/login`

**Description:**  
Authenticates a captain and returns a JWT token.

**Request Body:**
```json
{
  "email": "string",
  "password": "string"
}
```

**Validation Rules:**
- `email`: Must be a valid email format
- `password`: Must not be empty

**Responses:**

| Status Code | Description |
|-------------|-------------|
| 200         | Login successful |
| 400         | Invalid credentials or validation error |

**Success Response Example:**
```json
{
  "token": "jwt-token",
  "captain": {
    "fullName": {
      "firstName": "John",
      "lastName": "Doe"
    },
    "email": "<EMAIL>",
    "_id": "captain-id",
    "vehicle": {
      "color": "Black",
      "plateNumber": "ABC123",
      "capacity": 4,
      "vehicleType": "sedan"
    }
  }
}
```

**Error Response Example:**
```json
{
  "message": "Invalid email or password"
}
```

**Validation Error Example:**
```json
{
  "errors": [
    {
      "msg": "Email is invalid",
      "param": "email",
      "location": "body"
    }
  ]
}
```

### Get Captain Profile
**Endpoint:** `GET /captains/profile`

**Description:**  
Retrieves the authenticated captain's profile information.

**Authentication:**  
Requires a valid JWT token in the Authorization header.

**Request Body:**
None

**Responses:**

| Status Code | Description |
|-------------|-------------|
| 200         | Success |
| 401         | Unauthorized - Invalid or missing token |
| 500         | Server error |

**Success Response Example:**
```json
{
  "captain": {
    "fullName": {
      "firstName": "John",
      "lastName": "Doe"
    },
    "email": "<EMAIL>",
    "_id": "captain-id",
    "vehicle": {
      "color": "Black",
      "plateNumber": "ABC123",
      "capacity": 4,
      "vehicleType": "sedan"
    }
  }
}
```

### Logout Captain
**Endpoint:** `GET /captains/logout`

**Description:**  
Logs out the authenticated captain.

**Authentication:**  
Requires a valid JWT token in the Authorization header.

**Request Body:**
None

**Responses:**

| Status Code | Description |
|-------------|-------------|
| 200         | Logout successful |
| 401         | Unauthorized - Invalid or missing token |
| 500         | Server error |

**Success Response Example:**
```json
{
  "message": "Logged out successfully"
}
```

**Error Response Example:**
```json
{
  "message": "Server error",
  "error": "Error details"
}
```

### Update Captain Profile
**Endpoint:** `PUT /captains/profile`

**Description:**  
Updates the authenticated captain's profile information.

**Authentication:**  
Requires a valid JWT token in the x-auth-token header.

**Request Body:**
```json
{
  "fullName": {
    "firstName": "string",
    "lastName": "string"
  },
  "vehicle": {
    "color": "string",
    "plateNumber": "string",
    "capacity": "number",
    "vehicleType": "string"
  }
}
```

**Responses:**

| Status Code | Description |
|-------------|-------------|
| 200         | Profile updated successfully |
| 401         | Unauthorized - Invalid or missing token |
| 404         | Captain not found |

**Success Response Example:**
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "captain": {
    "fullName": {
      "firstName": "John",
      "lastName": "Smith"
    },
    "email": "<EMAIL>",
    "vehicle": {
      "color": "Blue",
      "plateNumber": "XYZ789",
      "capacity": 4,
      "vehicleType": "sedan"
    }
  }
}
```

### Update Captain Location
**Endpoint:** `PUT /captains/location`

**Description:**  
Updates the captain's current location coordinates.

**Authentication:**  
Requires a valid JWT token in the x-auth-token header.

**Request Body:**
```json
{
  "location": {
    "lat": "number",
    "lng": "number"
  }
}
```

**Validation Rules:**
- `location.lat`: Must be a number
- `location.lng`: Must be a number

**Responses:**

| Status Code | Description |
|-------------|-------------|
| 200         | Location updated successfully |
| 400         | Validation error |
| 401         | Unauthorized - Invalid or missing token |
| 404         | Captain not found |

**Success Response Example:**
```json
{
  "success": true,
  "message": "Location updated successfully",
  "location": {
    "lat": 37.7749,
    "lng": -122.4194
  }
}
```

### Update Captain Status
**Endpoint:** `PUT /captains/status`

**Description:**  
Updates the captain's current availability status.

**Authentication:**  
Requires a valid JWT token in the x-auth-token header.

**Request Body:**
```json
{
  "status": "string"
}
```

**Validation Rules:**
- `status`: Must be one of: 'offline', 'online', 'busy'

**Responses:**

| Status Code | Description |
|-------------|-------------|
| 200         | Status updated successfully |
| 400         | Validation error |
| 401         | Unauthorized - Invalid or missing token |
| 404         | Captain not found |

**Success Response Example:**
```json
{
  "success": true,
  "message": "Status updated successfully",
  "status": "busy"
}
```

### Update Captain Socket ID
**Endpoint:** `PUT /captains/socket`

**Description:**  
Updates the captain's socket ID for real-time communication.

**Authentication:**  
Requires a valid JWT token in the x-auth-token header.

**Request Body:**
```json
{
  "socketId": "string"
}
```

**Validation Rules:**
- `socketId`: Must not be empty

**Responses:**

| Status Code | Description |
|-------------|-------------|
| 200         | Socket ID updated successfully |
| 400         | Validation error |
| 401         | Unauthorized - Invalid or missing token |
| 404         | Captain not found |

**Success Response Example:**
```json
{
  "success": true,
  "message": "Socket ID updated successfully"
}
```

## Environment Variables Required
```
GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
```

## Map Service Endpoints
`
### Base URL: `/maps`

---

## 1. Geocoding

### Get Coordinates from Address
**Endpoint:** `GET /maps/coordinates`

**Description:** Convert an address to latitude/longitude coordinates.

**Query Parameters:**
- `address` (required): The address to geocode

**Example Request:**
```
GET /maps/coordinates?address=Times Square, New York, NY
```

**Response:**
```json
{
  "success": true,
  "data": {
    "lat": 40.7580,
    "lng": -73.9855,
    "formatted_address": "Times Square, New York, NY 10036, USA"
  }
}
```

### Get Address from Coordinates
**Endpoint:** `GET /maps/address`

**Description:** Convert latitude/longitude coordinates to an address.

**Query Parameters:**
- `lat` (required): Latitude (-90 to 90)
- `lng` (required): Longitude (-180 to 180)

**Example Request:**
```
GET /maps/address?lat=40.7580&lng=-73.9855
```

**Response:**
```json
{
  "success": true,
  "data": {
    "formatted_address": "Times Square, New York, NY 10036, USA",
    "address_components": [...]
  }
}
```

---

## 2. Routing

### Get Route Information
**Endpoint:** `GET /maps/route`

**Description:** Get route information between two points.

**Query Parameters:**
- `origin` (required): Starting point (address or coordinates)
- `destination` (required): End point (address or coordinates)
- `mode` (optional): Travel mode (driving, walking, bicycling, transit)

**Example Request:**
```
GET /maps/route?origin=Times Square, NY&destination=Central Park, NY&mode=driving
```

**Response:**
```json
{
  "success": true,
  "data": {
    "distance": {
      "text": "2.1 km",
      "value": 2100
    },
    "duration": {
      "text": "8 mins",
      "value": 480
    },
    "start_location": { "lat": 40.7580, "lng": -73.9855 },
    "end_location": { "lat": 40.7829, "lng": -73.9654 },
    "steps": [...],
    "overview_polyline": "encoded_polyline_string"
  }
}
```

### Get Traffic Information
**Endpoint:** `GET /maps/traffic`

**Description:** Get real-time traffic information for a route.

**Query Parameters:**
- `origin` (required): Starting point
- `destination` (required): End point

**Response:**
```json
{
  "success": true,
  "data": {
    "normal_duration": { "text": "8 mins", "value": 480 },
    "traffic_duration": { "text": "12 mins", "value": 720 },
    "delay_seconds": 240,
    "traffic_level": "moderate",
    "delay_text": "4 min delay"
  }
}
```

---

## 3. Places and Autocomplete

### Get Autocomplete Suggestions
**Endpoint:** `GET /maps/autocomplete`

**Description:** Get place suggestions based on user input.

**Query Parameters:**
- `input` (required): Search query
- `lat` (optional): Latitude for location bias
- `lng` (optional): Longitude for location bias
- `radius` (optional): Search radius in meters (default: 50000)

**Example Request:**
```
GET /maps/autocomplete?input=coffee shop&lat=40.7580&lng=-73.9855
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "place_id": "ChIJ...",
      "description": "Starbucks Coffee - Times Square",
      "main_text": "Starbucks Coffee",
      "secondary_text": "Times Square, New York, NY",
      "types": ["cafe", "establishment"]
    }
  ]
}
```

### Get Place Details
**Endpoint:** `GET /maps/place/:placeId`

**Description:** Get detailed information about a specific place.

**Response:**
```json
{
  "success": true,
  "data": {
    "place_id": "ChIJ...",
    "name": "Starbucks Coffee",
    "formatted_address": "1560 Broadway, New York, NY 10036",
    "location": { "lat": 40.7580, "lng": -73.9855 },
    "types": ["cafe", "establishment"],
    "rating": 4.2
  }
}
```

---

## 4. Distance and Fare Calculation

### Calculate Distance
**Endpoint:** `GET /maps/distance`

**Description:** Calculate distance between two points.

**Query Parameters:**
- `lat1`, `lng1` (required): First point coordinates
- `lat2`, `lng2` (required): Second point coordinates

**Response:**
```json
{
  "success": true,
  "data": {
    "kilometers": 2.1,
    "miles": 1.3,
    "meters": 2100
  }
}
```

### Calculate Estimated Fare
**Endpoint:** `GET /maps/fare`

**Description:** Calculate estimated ride fare.

**Query Parameters:**
- `distance` (required): Distance in meters
- `duration` (required): Duration in seconds
- `vehicleType` (optional): Vehicle type (sedan, suv, van, auto, motorcycle, bicycle)

**Response:**
```json
{
  "success": true,
  "data": {
    "baseFare": 50,
    "distanceFare": 31.5,
    "timeFare": 16,
    "totalFare": 98,
    "minimumFare": 80,
    "breakdown": {
      "distance": "2.10 km",
      "duration": "8 min",
      "vehicleType": "sedan"
    }
  }
}
```

---

## 5. Captain Services

### Find Nearby Captains
**Endpoint:** `GET /maps/nearby-captains`

**Description:** Find available captains near a location.

**Authentication:** Required (User token)

**Query Parameters:**
- `lat`, `lng` (required): Location coordinates
- `radius` (optional): Search radius in km (default: 5)
- `status` (optional): Captain status filter (default: 'online')

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "captain_id",
      "fullName": { "firstName": "John", "lastName": "Doe" },
      "vehicle": {
        "vehicleType": "sedan",
        "plateNumber": "ABC123",
        "color": "Black"
      },
      "location": { "lat": 40.7580, "lng": -73.9855 },
      "status": "online"
    }
  ],
  "count": 1
}
```

---

## 6. Utility Endpoints

### Get Static Map URL
**Endpoint:** `GET /maps/static-map`

**Description:** Generate a static map image URL.

**Query Parameters:**
- `center` (required): Center coordinates (lat,lng)
- `zoom` (optional): Zoom level (1-20, default: 15)
- `size` (optional): Image size (WIDTHxHEIGHT, default: 600x400)
- `markers` (optional): JSON array of markers

**Example Request:**
```
GET /maps/static-map?center=40.7580,-73.9855&zoom=15&markers=[{"lat":40.7580,"lng":-73.9855,"color":"red","label":"A"}]
```

**Response:**
```json
{
  "success": true,
  "data": {
    "url": "https://maps.googleapis.com/maps/api/staticmap?..."
  }
}
```

---

## Captain Location Management

### Update Captain Location
**Endpoint:** `PUT /captains/location`

**Description:** Update captain's current location.

**Authentication:** Required (Captain token)

**Request Body:**
```json
{
  "location": {
    "lat": 40.7580,
    "lng": -73.9855
  }
}
```

### Update Captain Status
**Endpoint:** `PUT /captains/status`

**Description:** Update captain's availability status.

**Authentication:** Required (Captain token)

**Request Body:**
```json
{
  "status": "online"
}
```

---

## Error Responses

All endpoints return errors in the following format:

```json
{
  "success": false,
  "message": "Error description"
}
```

Common HTTP status codes:
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (missing/invalid token)
- `404`: Not Found
- `500`: Internal Server Error

---

## Rate Limiting

Google Maps API has usage limits. Monitor your usage in the Google Cloud Console and implement appropriate rate limiting if needed.

## Ride Management Endpoints

### Base URL: `/rides`

### Create Ride Request
**Endpoint:** `POST /rides/create`

**Description:** Create a new ride request.

**Authentication:** Required (User token)

**Request Body:**
```json
{
  "pickup": {
    "address": "Times Square, New York, NY",
    "coordinates": { "lat": 40.7580, "lng": -73.9855 }
  },
  "destination": {
    "address": "Central Park, New York, NY",
    "coordinates": { "lat": 40.7829, "lng": -73.9654 }
  },
  "vehicleType": "sedan"
}
```

### Find Nearby Captains for Ride
**Endpoint:** `GET /rides/:rideId/nearby-captains`

**Authentication:** Required (User token)

### Accept Ride (Captain)
**Endpoint:** `POST /rides/:rideId/accept`

**Authentication:** Required (Captain token)

### Start Ride (Captain)
**Endpoint:** `POST /rides/:rideId/start`

**Authentication:** Required (Captain token)

**Request Body:**
```json
{
  "otp": "1234"
}
```

### Complete Ride (Captain)
**Endpoint:** `POST /rides/:rideId/complete`

**Authentication:** Required (Captain token)

### Cancel Ride
**Endpoint:** `POST /rides/:rideId/cancel`

**Authentication:** Required (User or Captain token)

**Request Body:**
```json
{
  "reason": "Traffic delay"
}
```

### Get Ride Details
**Endpoint:** `GET /rides/:rideId`

**Authentication:** Required (User or Captain token)

---

## Security Notes

1. Never expose your Google Maps API key in client-side code
2. Restrict your API key to specific domains/IPs in production
3. Enable only the required Google Maps services for your API key
4. Monitor API usage for unusual patterns
