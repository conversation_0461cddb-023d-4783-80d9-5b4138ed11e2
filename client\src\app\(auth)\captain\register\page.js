'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Fa<PERSON>ser, FaEnvelope, FaLock, FaEye, FaEyeSlash, FaPhone, FaCar, FaIdCard, FaPalette, FaUsers, FaArrowRight, FaArrowLeft, FaCheck } from 'react-icons/fa';

export default function CaptainRegisterPage() {
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  // Form state
  const [formData, setFormData] = useState({
    fullName: {
      firstName: '',
      lastName: '',
    },
    email: '',
    password: '',
    confirmPassword: '',
    phoneNumber: '',
    vehicle: {
      color: '',
      plateNumber: '',
      capacity: 4,
      vehicleType: 'sedan'
    }
  });
  
  // Password visibility toggling
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData({
        ...formData,
        [parent]: {
          ...formData[parent],
          [child]: value
        }
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };
  
  const validateStep = (step) => {
    switch(step) {
      case 1:
        if (!formData.fullName.firstName || !formData.fullName.lastName) {
          setError('Please enter your full name');
          return false;
        }
        if (!formData.phoneNumber) {
          setError('Please enter your phone number');
          return false;
        }
        setError('');
        return true;
        
      case 2:
        if (!formData.email) {
          setError('Please enter your email');
          return false;
        }
        if (!formData.password) {
          setError('Please enter a password');
          return false;
        }
        if (formData.password !== formData.confirmPassword) {
          setError('Passwords do not match');
          return false;
        }
        setError('');
        return true;
        
      case 3:
        if (!formData.vehicle.vehicleType) {
          setError('Please select a vehicle type');
          return false;
        }
        if (!formData.vehicle.color) {
          setError('Please enter your vehicle color');
          return false;
        }
        if (!formData.vehicle.plateNumber) {
          setError('Please enter your license plate number');
          return false;
        }
        setError('');
        return true;
        
      default:
        return true;
    }
  };
  
  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep((prev) => Math.min(prev + 1, 4));
    }
  };
  
  const prevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1));
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateStep(currentStep)) {
      return;
    }
    
    try {
      setLoading(true);
      
      // Create the submission object (remove confirmPassword)
      const submissionData = {
        fullName: formData.fullName,
        email: formData.email,
        password: formData.password,
        phoneNumber: formData.phoneNumber,
        vehicle: formData.vehicle
      };
      
      // Example API call:
      console.log('Registering captain:', submissionData);
      // const response = await fetch('/api/captain/register', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(submissionData)
      // });
      
      // if (!response.ok) {
      //   throw new Error('Registration failed');
      // }
      
      // Move to success step
      setCurrentStep(5);
    } catch (err) {
      setError('Registration failed. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Render different form steps
  const renderStep = () => {
    switch(currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold">Personal Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">
                  First Name
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaUser className="text-gray-400" />
                  </div>
                  <input
                    id="firstName"
                    name="fullName.firstName"
                    type="text"
                    required
                    className="pl-10 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm py-2 border"
                    placeholder="John"
                    value={formData.fullName.firstName}
                    onChange={handleChange}
                  />
                </div>
              </div>
              
              <div>
                <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">
                  Last Name
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaUser className="text-gray-400" />
                  </div>
                  <input
                    id="lastName"
                    name="fullName.lastName"
                    type="text"
                    required
                    className="pl-10 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm py-2 border"
                    placeholder="Doe"
                    value={formData.fullName.lastName}
                    onChange={handleChange}
                  />
                </div>
              </div>
            </div>

            <div>
              <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700">
                Phone Number
              </label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaPhone className="text-gray-400" />
                </div>
                <input
                  id="phoneNumber"
                  name="phoneNumber"
                  type="tel"
                  required
                  className="pl-10 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm py-2 border"
                  placeholder="(*************"
                  value={formData.phoneNumber}
                  onChange={handleChange}
                />
              </div>
            </div>
          </div>
        );
        
      case 2:
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold">Account Details</h2>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email Address
              </label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaEnvelope className="text-gray-400" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  className="pl-10 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm py-2 border"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={handleChange}
                />
              </div>
            </div>
            
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaLock className="text-gray-400" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  autoComplete="new-password"
                  required
                  className="pl-10 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm py-2 border"
                  value={formData.password}
                  onChange={handleChange}
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <button
                    type="button"
                    className="text-gray-400 hover:text-gray-600 focus:outline-none"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <FaEyeSlash /> : <FaEye />}
                  </button>
                </div>
              </div>
            </div>
            
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                Confirm Password
              </label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaLock className="text-gray-400" />
                </div>
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  autoComplete="new-password"
                  required
                  className="pl-10 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm py-2 border"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <button
                    type="button"
                    className="text-gray-400 hover:text-gray-600 focus:outline-none"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                  </button>
                </div>
              </div>
            </div>
          </div>
        );
        
      case 3:
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold">Vehicle Information</h2>
            <div>
              <label htmlFor="vehicleType" className="block text-sm font-medium text-gray-700">
                Vehicle Type
              </label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaCar className="text-gray-400" />
                </div>
                <select
                  id="vehicleType"
                  name="vehicle.vehicleType"
                  className="pl-10 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm py-2 border"
                  value={formData.vehicle.vehicleType}
                  onChange={handleChange}
                >
                  <option value="sedan">Sedan</option>
                  <option value="suv">SUV</option>
                  <option value="minivan">Minivan</option>
                  <option value="luxury">Luxury</option>
                  <option value="motorcycle">Motorcycle</option>
                </select>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="color" className="block text-sm font-medium text-gray-700">
                  Vehicle Color
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaPalette className="text-gray-400" />
                  </div>
                  <input
                    id="color"
                    name="vehicle.color"
                    type="text"
                    required
                    className="pl-10 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm py-2 border"
                    placeholder="White"
                    value={formData.vehicle.color}
                    onChange={handleChange}
                  />
                </div>
              </div>
              
              <div>
                <label htmlFor="plateNumber" className="block text-sm font-medium text-gray-700">
                  License Plate Number
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaIdCard className="text-gray-400" />
                  </div>
                  <input
                    id="plateNumber"
                    name="vehicle.plateNumber"
                    type="text"
                    required
                    className="pl-10 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm py-2 border"
                    placeholder="ABC-1234"
                    value={formData.vehicle.plateNumber}
                    onChange={handleChange}
                  />
                </div>
              </div>
            </div>
            
            <div>
              <label htmlFor="capacity" className="block text-sm font-medium text-gray-700">
                Vehicle Capacity (number of passengers)
              </label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaUsers className="text-gray-400" />
                </div>
                <select
                  id="capacity"
                  name="vehicle.capacity"
                  className="pl-10 block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black sm:text-sm py-2 border"
                  value={formData.vehicle.capacity}
                  onChange={(e) => handleChange({
                    target: {
                      name: e.target.name,
                      value: parseInt(e.target.value)
                    }
                  })}
                >
                  <option value="1">1</option>
                  <option value="2">2</option>
                  <option value="3">3</option>
                  <option value="4">4</option>
                  <option value="6">6</option>
                  <option value="8">8</option>
                </select>
              </div>
            </div>
          </div>
        );
        
      case 4:
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold">Review Your Information</h2>
            <div className="bg-gray-50 p-4 rounded-md">
              <h3 className="font-medium text-gray-700 mb-2">Personal Details</h3>
              <p className="text-sm"><span className="font-semibold">Name:</span> {formData.fullName.firstName} {formData.fullName.lastName}</p>
              <p className="text-sm"><span className="font-semibold">Phone:</span> {formData.phoneNumber}</p>
              <p className="text-sm"><span className="font-semibold">Email:</span> {formData.email}</p>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-md">
              <h3 className="font-medium text-gray-700 mb-2">Vehicle Details</h3>
              <p className="text-sm"><span className="font-semibold">Type:</span> {formData.vehicle.vehicleType.charAt(0).toUpperCase() + formData.vehicle.vehicleType.slice(1)}</p>
              <p className="text-sm"><span className="font-semibold">Color:</span> {formData.vehicle.color}</p>
              <p className="text-sm"><span className="font-semibold">Plate Number:</span> {formData.vehicle.plateNumber}</p>
              <p className="text-sm"><span className="font-semibold">Capacity:</span> {formData.vehicle.capacity} passengers</p>
            </div>
            
            <div className="flex items-center">
              <input
                id="terms"
                name="terms"
                type="checkbox"
                required
                className="h-4 w-4 text-black focus:ring-black border-gray-300 rounded"
              />
              <label htmlFor="terms" className="ml-2 block text-sm text-gray-900">
                I agree to the <a href="/terms" className="text-black font-medium">Terms of Service</a>, <a href="/privacy" className="text-black font-medium">Privacy Policy</a>, and <a href="/captain-agreement" className="text-black font-medium">Captain Agreement</a>
              </label>
            </div>
          </div>
        );
        
      case 5:
        return (
          <div className="text-center space-y-6">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
              <FaCheck className="h-6 w-6 text-green-600" />
            </div>
            <h2 className="text-2xl font-semibold">Registration Successful!</h2>
            <p className="text-gray-600">
              Your account has been created. Our team will review your information and will notify you once your account is approved.
            </p>
            <div>
              <Link 
                href="/captain/login" 
                className="inline-block bg-black text-white py-2 px-4 rounded-md font-medium hover:bg-gray-800 transition"
              >
                Go to Login
              </Link>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 text-black py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <div className="text-center">
          <h1 className="text-3xl font-extrabold text-gray-900">Become a Captain</h1>
          <p className="mt-2 text-sm text-gray-600">Join our network of professional drivers</p>
        </div>
        
        {/* Progress bar */}
        {currentStep < 5 && (
          <div className="mt-8">
            <div className="relative">
              <div className="overflow-hidden h-2 mb-4 text-xs flex bg-gray-200 rounded">
                <div 
                  style={{ width: `${(currentStep/4) * 100}%` }} 
                  className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-black transition-all duration-300"
                ></div>
              </div>
              <div className="flex justify-between">
                <div className={`text-xs ${currentStep >= 1 ? 'text-black font-medium' : 'text-gray-400'}`}>Personal</div>
                <div className={`text-xs ${currentStep >= 2 ? 'text-black font-medium' : 'text-gray-400'}`}>Account</div>
                <div className={`text-xs ${currentStep >= 3 ? 'text-black font-medium' : 'text-gray-400'}`}>Vehicle</div>
                <div className={`text-xs ${currentStep >= 4 ? 'text-black font-medium' : 'text-gray-400'}`}>Review</div>
              </div>
            </div>
          </div>
        )}
        
        <div className="mt-8 bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {/* Error message */}
          {error && (
            <div className="mb-4 bg-red-50 border border-red-200 text-red-600 p-3 rounded-md text-sm">
              {error}
            </div>
          )}
          
          <form onSubmit={handleSubmit}>
            {/* Form content based on current step */}
            {renderStep()}
            
            {/* Navigation buttons */}
            {currentStep < 5 && (
              <div className="mt-8 flex justify-between">
                {currentStep > 1 ? (
                  <button
                    type="button"
                    onClick={prevStep}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <FaArrowLeft className="mr-2 h-4 w-4" />
                    Back
                  </button>
                ) : (
                  <div></div>
                )}
                
                {currentStep < 4 ? (
                  <button
                    type="button"
                    onClick={nextStep}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800"
                  >
                    Next
                    <FaArrowRight className="ml-2 h-4 w-4" />
                  </button>
                ) : (
                  <button
                    type="submit"
                    disabled={loading}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800"
                  >
                    {loading ? 'Submitting...' : 'Submit Application'}
                  </button>
                )}
              </div>
            )}
          </form>
        </div>
        
        {/* Already have an account link */}
        <p className="mt-4 text-center text-sm text-gray-600">
          Already a captain?{' '}
          <Link href="/captain/login" className="font-medium text-black hover:text-gray-800">
            Log in here
          </Link>
        </p>
      </div>
    </div>
  );
}
