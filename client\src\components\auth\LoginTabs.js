'use client';

export default function LoginTabs({ activeTab, onTabChange }) {
  return (
    <div className="flex border-b mb-6">
      <button
        className={`flex-1 py-2 font-medium text-sm ${
          activeTab === 'email'
            ? 'text-black border-b-2 border-black'
            : 'text-gray-500 hover:text-gray-700'
        }`}
        onClick={() => onTabChange('email')}
      >
        Email
      </button>
      <button
        className={`flex-1 py-2 font-medium text-sm ${
          activeTab === 'phone'
            ? 'text-black border-b-2 border-black'
            : 'text-gray-500 hover:text-gray-700'
        }`}
        onClick={() => onTabChange('phone')}
      >
        Phone
      </button>
    </div>
  );
}
