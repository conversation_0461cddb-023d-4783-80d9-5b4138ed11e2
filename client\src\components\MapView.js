import { motion } from 'framer-motion';

export default function MapView({ isMobile, location, destination, isVehicleSelectionMode }) {
  return (
    <div className={`absolute font-[body] md:relative inset-0 md:order-2 md:w-3/5 bg-gray-200 flex-grow transition-all duration-300 ${isVehicleSelectionMode ? 'opacity-100' : 'opacity-90'}`}>
      {/* Basic placeholder for the map */}
      <div className="w-full h-full flex items-center justify-center">
        {isVehicleSelectionMode && (location && destination) ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="w-full h-full flex flex-col items-center justify-center"
          >
            {/* Simulated route on the map */}
            <svg 
              className="w-full max-w-md" 
              height="200" 
              viewBox="0 0 400 200" 
              fill="none" 
              xmlns="http://www.w3.org/2000/svg"
            >
              <motion.path 
                d="M50,150 C100,50 300,150 350,50" 
                stroke="black" 
                strokeWidth="3" 
                strokeLinecap="round" 
                strokeDasharray="400"
                strokeDashoffset="400"
                animate={{ strokeDashoffset: 0 }}
                transition={{ duration: 2, ease: "easeInOut" }}
              />
              <motion.circle 
                cx="50" cy="150" r="8" 
                fill="black"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.5 }}
              />
              <motion.circle 
                cx="350" cy="50" r="8" 
                fill="black" 
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 2.5 }}
              />
            </svg>
            <div className="text-center mt-4 px-4">
              <p className="text-lg font-medium">Distance: ~5.7 miles</p>
              <p className="text-gray-500">Estimated travel time: 15-20 min</p>
            </div>
          </motion.div>
        ) : (
          <div className="text-gray-500">
            <svg className="w-16 h-16 mx-auto opacity-50" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"></path>
            </svg>
            <p className="mt-2">Enter locations to see the route</p>
          </div>
        )}
      </div>
    </div>
  );
}
