"use client";
import React, { useEffect } from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import Link from "next/link";
import { useRouter } from "next/navigation";

const Page = () => {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gray-900 font-[body] text-gray-100">
      {/* Navigation Bar */}
      <nav className="fixed w-full bg-transparent z-50 px-6 py-4">
        <div className="flex justify-between items-center">
          <motion.h1 
            className="text-3xl font-bold font-[head] text-white"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            RideUp
          </motion.h1>
          <motion.div 
            className="hidden md:flex space-x-8"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.7 }}
          >
            <Link href="#" className="text-white hover:text-gray-300 transition">Home</Link>
            <Link href="#services" className="text-white hover:text-gray-300 transition">Services</Link>
            <Link href="#about" className="text-white hover:text-gray-300 transition">About</Link>
            <Link href="#contact" className="text-white hover:text-gray-300 transition">Contact</Link>
          </motion.div>
          <motion.button 
          onClick={() => router.push('/in/en/ride')}
            className="bg-white text-black px-6 py-2 rounded-full font-medium hover:bg-opacity-90 transition"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            Book Now
          </motion.button>
        </div>
      </nav>

      {/* Hero Section */}
      <div className="relative h-screen w-full">
        {/* Background Image with darker overlay */}
        <div className="absolute inset-0 z-0">
          <Image
            src="https://images.unsplash.com/photo-1557404763-69708cd8b9ce?q=80&w=1528&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
            alt="Luxury car"
            fill
            style={{ objectFit: "cover" }}
            priority
            quality={100}
          />
          <div className="absolute inset-0 bg-black opacity-[70%] z-10"></div>
        </div>

        {/* Hero Content */}
        <div className="relative z-20 flex flex-col items-center justify-center h-screen text-white text-center px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-5xl md:text-7xl font-bold font-[head] mb-4">Luxury Rides Made Simple</h1>
            <p className="text-xl md:text-2xl max-w-2xl mx-auto mb-8 font-[body]">
              Experience the ultimate in comfort and style with our premium car service
            </p>
          </motion.div>
          
          <motion.div 
            className="flex flex-col md:flex-row gap-4"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.8 }}
          >
            <button className="bg-white text-black px-8 py-3 rounded-full text-lg font-medium hover:bg-opacity-90 transition">
              Book a Ride
            </button>
            <button className="bg-transparent border-2 border-white text-white px-8 py-3 rounded-full text-lg font-medium hover:bg-white hover:bg-opacity-10 transition">
              Our Fleet
            </button>
          </motion.div>

          <motion.div 
            className="absolute bottom-10 left-0 right-0"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1, duration: 1 }}
          >
            <div className="flex justify-center">
              <div className="animate-bounce bg-white p-2 w-10 h-10 ring-1 ring-slate-200 shadow-lg rounded-full flex items-center justify-center">
                <svg className="w-6 h-6 text-black" fill="none" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" viewBox="0 0 24 24" stroke="currentColor">
                  <path d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                </svg>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Features Section */}
      <section id="services" className="py-20 px-6 bg-gray-800">
        <div className="max-w-6xl mx-auto">
          <motion.div 
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold mb-4 font-[head] text-white">Why Choose RideUp</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto font-[body]">
              We offer premium services tailored to your comfort and convenience
            </p>
          </motion.div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {[
              { title: "Premium Fleet", desc: "Access to luxury vehicles for any occasion" },
              { title: "Professional Drivers", desc: "Experienced chauffeurs at your service 24/7" },
              { title: "Seamless Booking", desc: "Easy reservation process with instant confirmation" }
            ].map((feature, index) => (
              <motion.div 
                key={index}
                className="bg-gray-700 p-8 rounded-xl shadow-md hover:shadow-lg transition border border-gray-600"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2, duration: 0.5 }}
                viewport={{ once: true }}
              >
                <h3 className="text-2xl font-bold mb-3 font-[head] text-white">{feature.title}</h3>
                <p className="text-gray-300 font-[body]">{feature.desc}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-20 px-6 bg-gray-900 border-t border-gray-800">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row items-center gap-12">
            <motion.div 
              className="md:w-1/2"
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="relative h-[400px] w-full rounded-lg overflow-hidden shadow-xl">
                <Image 
                  src="https://images.unsplash.com/photo-1549275301-c9d60945be6b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80" 
                  alt="Premium car interior" 
                  fill 
                  style={{ objectFit: "cover" }} 
                />
                <div className="absolute inset-0 bg-black opacity-20"></div>
              </div>
            </motion.div>

            <motion.div 
              className="md:w-1/2"
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-4xl font-bold mb-6 font-[head] text-white">About RideUp</h2>
              <p className="text-lg text-gray-300 mb-6 font-[body]">
                Founded in 2020, RideUp has revolutionized the premium transportation industry by combining luxury with convenience.
              </p>
              <p className="text-lg text-gray-300 mb-6 font-[body]">
                Our mission is to provide exceptional travel experiences with a fleet of high-end vehicles and professional drivers who understand the importance of reliability, comfort, and discretion.
              </p>
              <p className="text-lg font-[bodyitalic] text-gray-400">
                "We don't just drive you to your destination; we elevate your journey."
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Fleet Showcase */}
      <section className="py-20 px-6 bg-gray-800">
        <div className="max-w-6xl mx-auto">
          <motion.div 
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold mb-4 font-[head] text-white">Our Premium Fleet</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto font-[body]">
              Choose from our selection of high-end vehicles for any occasion
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              { name: "Executive Sedan", image: "https://images.unsplash.com/photo-1550355291-bbee04a92027?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1036&q=80", desc: "Perfect for business travel" },
              { name: "Luxury SUV", image: "https://images.unsplash.com/photo-1533473359331-0135ef1b58bf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80", desc: "Spacious comfort for groups" },
              { name: "Sports Car", image: "https://images.unsplash.com/photo-1503376780353-7e6692767b70?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80", desc: "For those special moments" }
            ].map((car, index) => (
              <motion.div
                key={index}
                className="bg-gray-700 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition border border-gray-600"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2, duration: 0.5 }}
                viewport={{ once: true }}
              >
                <div className="relative h-64 w-full">
                  <Image 
                    src={car.image} 
                    alt={car.name} 
                    fill 
                    style={{ objectFit: "cover" }} 
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-2xl font-bold mb-2 font-[head] text-white">{car.name}</h3>
                  <p className="text-gray-300 mb-4 font-[body]">{car.desc}</p>
                  <button className="text-blue-400 font-medium hover:text-blue-300 transition font-[body]">
                    Learn More →
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 px-6 bg-black text-white">
        <div className="max-w-6xl mx-auto">
          <motion.div 
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold mb-4 font-[head]">What Our Clients Say</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto font-[body]">
              Hear from our satisfied customers about their RideUp experience
            </p>
          </motion.div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              { text: "RideUp has transformed my business travel experience. The drivers are always punctual and professional.", author: "Sarah Johnson", title: "Marketing Executive" },
              { text: "I used RideUp for my wedding day transportation and it was absolutely perfect. Elegant, comfortable, and stress-free.", author: "Michael Chen", title: "Happy Client" },
              { text: "As someone who travels frequently, I appreciate the consistency and reliability that RideUp provides every single time.", author: "David Rodriguez", title: "Business Consultant" },
            ].map((testimonial, index) => (
              <motion.div
                key={index}
                className="bg-gray-900 p-8 rounded-xl border border-gray-800"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2, duration: 0.5 }}
                viewport={{ once: true }}
              >
                <p className="text-lg mb-6 font-[bodyitalic] text-gray-300">"{testimonial.text}"</p>
                <div>
                  <p className="font-medium font-[body]">{testimonial.author}</p>
                  <p className="text-gray-400 font-[body]">{testimonial.title}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 px-6 bg-gray-900 border-t border-gray-800">
        <div className="max-w-4xl mx-auto">
          <motion.div 
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold mb-4 font-[head] text-white">Get in Touch</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto font-[body]">
              Have questions or ready to book? Reach out to our team.
            </p>
          </motion.div>
          
          <motion.div
            className="bg-gray-800 p-8 rounded-xl shadow-md border border-gray-700"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <form>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-gray-300 mb-2 font-[body]" htmlFor="name">Name</label>
                  <input type="text" id="name" className="w-full px-4 py-3 rounded-lg bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500" />
                </div>
                <div>
                  <label className="block text-gray-300 mb-2 font-[body]" htmlFor="email">Email</label>
                  <input type="email" id="email" className="w-full px-4 py-3 rounded-lg bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500" />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-gray-300 mb-2 font-[body]" htmlFor="message">Message</label>
                  <textarea id="message" rows="4" className="w-full px-4 py-3 rounded-lg bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                </div>
                <div className="md:col-span-2">
                  <button type="submit" className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition font-[body]">
                    Send Message
                  </button>
                </div>
              </div>
            </form>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black text-white py-12 px-6">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-2xl font-bold mb-4 font-[head]">RideUp</h3>
              <p className="text-gray-400 font-[body]">Luxury transportation redefined</p>
            </div>
            <div>
              <h4 className="text-lg font-bold mb-4 font-[head]">Services</h4>
              <ul className="space-y-2 font-[body]">
                <li><a href="#" className="text-gray-400 hover:text-white transition">Business Travel</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition">Airport Transfer</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition">Special Events</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition">City Tours</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-bold mb-4 font-[head]">Company</h4>
              <ul className="space-y-2 font-[body]">
                <li><a href="#" className="text-gray-400 hover:text-white transition">About Us</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition">Our Fleet</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition">Testimonials</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition">Contact</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-bold mb-4 font-[head]">Connect</h4>
              <div className="flex space-x-4 mb-4">
                <a href="#" className="bg-gray-800 hover:bg-gray-700 h-10 w-10 rounded-full flex items-center justify-center transition">
                  <span className="sr-only">Facebook</span>
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
                  </svg>
                </a>
                <a href="#" className="bg-gray-800 hover:bg-gray-700 h-10 w-10 rounded-full flex items-center justify-center transition">
                  <span className="sr-only">Instagram</span>
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fillRule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772a4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.63c2.43 0 2.784-.013 3.808-.06 1.064-.049 1.791-.218 2.427-.465a4.902 4.902 0 001.772-1.153 4.902 4.902 0 001.153-1.772c.247-.636.416-1.363.465-2.427.048-1.067.06-1.407.06-4.123v-.08c0-2.643-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clipRule="evenodd" />
                  </svg>
                </a>
                <a href="#" className="bg-gray-800 hover:bg-gray-700 h-10 w-10 rounded-full flex items-center justify-center transition">
                  <span className="sr-only">Twitter</span>
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                  </svg>
                </a>
              </div>
              <p className="text-gray-400 font-[body]">
                © 2023 RideUp. All rights reserved.
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Page;
