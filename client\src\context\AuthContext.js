'use client';

import { createContext, useContext, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  loginUser, 
  registerUser, 
  logoutUser, 
  loginCaptain, 
  registerCaptain, 
  logoutCaptain,
  getUserProfile,
  getCaptainProfile
} from '@/services/api';

// Create the context
const AuthContext = createContext();

// Custom hook for using the auth context
export const useAuth = () => useContext(AuthContext);

// AuthProvider component that wraps the application
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [captain, setCaptain] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const router = useRouter();

  // Check for saved auth state on initial load
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const token = localStorage.getItem('token');
        const authType = localStorage.getItem('authType');
        
        if (token) {
          if (authType === 'user') {
            const userData = await getUserProfile();
            setUser(userData.user);
          } else if (authType === 'captain') {
            const captainData = await getCaptainProfile();
            setCaptain(captainData.captain);
          }
        }
      } catch (err) {
        console.error('Auth initialization error:', err);
        localStorage.removeItem('token');
        localStorage.removeItem('authType');
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // User registration
  const registerUserAccount = async (userData) => {
    try {
      setLoading(true);
      setError('');
      console.log('Registering user:', userData);
      const data = await registerUser(userData);
      console.log(data)
      if (data.token) {
        localStorage.setItem('token', data.token);
        localStorage.setItem('authType', 'user');
        setUser(data.user);
        router.push(`/in/en/ride/${data.user._id}`); // Redirect to ride page instead of dashboard
      }
      
      return { success: true };
    } catch (err) {
      setError(err.message || 'Registration failed');
      return { success: false, error: err.message || 'Registration failed' };
    } finally {
      setLoading(false);
    }
  };

  // User login
  const loginUserAccount = async (credentials) => {
    try {
      setLoading(true);
      setError('');
      const data = await loginUser(credentials);
      
      if (data.token) {
        localStorage.setItem('token', data.token);
        localStorage.setItem('authType', 'user');
        setUser(data.user);
        router.push(`/in/en/ride/${data.user._id}`); // Redirect to user dashboard
      }
      
      return { success: true };
    } catch (err) {
      setError(err.message || 'Login failed');
      return { success: false, error: err.message || 'Login failed' };
    } finally {
      setLoading(false);
    }
  };

  // User logout
  const logoutUserAccount = async () => {
    try {
      setLoading(true);
      await logoutUser();
      localStorage.removeItem('token');
      localStorage.removeItem('authType');
      setUser(null);
      router.push('/user/login');
      return { success: true };
    } catch (err) {
      setError(err.message || 'Logout failed');
      return { success: false, error: err.message || 'Logout failed' };
    } finally {
      setLoading(false);
    }
  };

  // Captain registration
  const registerCaptainAccount = async (captainData) => {
    try {
      setLoading(true);
      setError('');
      const data = await registerCaptain(captainData);
      
      if (data.token) {
        localStorage.setItem('token', data.token);
        localStorage.setItem('authType', 'captain');
        setCaptain(data.captain);
        router.push('/captain/dashboard'); // Redirect to captain dashboard
      }
      
      return { success: true };
    } catch (err) {
      setError(err.message || 'Registration failed');
      return { success: false, error: err.message || 'Registration failed' };
    } finally {
      setLoading(false);
    }
  };

  // Captain login
  const loginCaptainAccount = async (credentials) => {
    try {
      setLoading(true);
      setError('');
      const data = await loginCaptain(credentials);
      
      if (data.token) {
        localStorage.setItem('token', data.token);
        localStorage.setItem('authType', 'captain');
        setCaptain(data.captain);
        router.push('/captain/dashboard'); // Redirect to captain dashboard
      }
      
      return { success: true };
    } catch (err) {
      setError(err.message || 'Login failed');
      return { success: false, error: err.message || 'Login failed' };
    } finally {
      setLoading(false);
    }
  };

  // Captain logout
  const logoutCaptainAccount = async () => {
    try {
      setLoading(true);
      await logoutCaptain();
      localStorage.removeItem('token');
      localStorage.removeItem('authType');
      setCaptain(null);
      router.push('/captain/login');
      return { success: true };
    } catch (err) {
      setError(err.message || 'Logout failed');
      return { success: false, error: err.message || 'Logout failed' };
    } finally {
      setLoading(false);
    }
  };

  // Check if user is authenticated
  const isUserAuthenticated = () => !!user;
  
  // Check if captain is authenticated
  const isCaptainAuthenticated = () => !!captain;

  // Clear any error messages
  const clearError = () => setError('');

  // Provide the context value
  const contextValue = {
    user,
    captain,
    loading,
    error,
    registerUserAccount,
    loginUserAccount,
    logoutUserAccount,
    registerCaptainAccount,
    loginCaptainAccount,
    logoutCaptainAccount,
    isUserAuthenticated,
    isCaptainAuthenticated,
    clearError
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};
