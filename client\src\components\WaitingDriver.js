import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

export default function WaitingDriver({ location, destination, vehicleType, paymentMethod }) {
  const [progress, setProgress] = useState(0);
  
  useEffect(() => {
    // Simulate loading progress
    const timer = setInterval(() => {
      setProgress(prev => {
        if (prev < 100) {
          return prev + 5;
        }
        clearInterval(timer);
        return 100;
      });
    }, 250);
    
    return () => clearInterval(timer);
  }, []);
  
  return (
    <div className="flex font-[body] flex-col items-center text-center h-full py-6">
      <h2 className="text-2xl font-bold mb-2">Finding Your Driver</h2>
      <p className="text-gray-600 mb-8">This usually takes 1-3 minutes</p>
      
      <motion.div 
        className="w-36 h-36 bg-gray-100 rounded-full flex items-center justify-center mb-8 relative"
        animate={{ 
          boxShadow: ["0px 0px 0px rgba(0,0,0,0.2)", "0px 0px 30px rgba(0,0,0,0.1)", "0px 0px 0px rgba(0,0,0,0.2)"]
        }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        {/* Progress circle */}
        <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 100">
          <circle
            cx="50"
            cy="50"
            r="40"
            fill="none"
            stroke="#e5e7eb"
            strokeWidth="8"
          />
          <motion.circle
            cx="50"
            cy="50"
            r="40"
            fill="none"
            stroke="black"
            strokeWidth="8"
            strokeLinecap="round"
            strokeDasharray="251.2"
            initial={{ strokeDashoffset: 251.2 }}
            animate={{ strokeDashoffset: 251.2 - (251.2 * progress) / 100 }}
            transition={{ duration: 0.5 }}
            style={{ transformOrigin: 'center', transform: 'rotate(-90deg)' }}
          />
        </svg>
        
        {/* Car animation */}
        <motion.div
          animate={{ 
            rotate: [0, 360],
          }}
          transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
          className="z-10"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 7h8m0 0l-4-4m4 4l-4 4m-4 6h8m-8 0l4 4m-4-4l4-4" />
          </svg>
        </motion.div>
      </motion.div>
      
      <div className="text-gray-800 mb-6">
        <p className="font-medium mb-1">Matching with nearby {vehicleType}</p>
        <p className="text-sm text-gray-600">Payment: {paymentMethod === 'cash' ? 'Cash' : 'Paid Online'}</p>
      </div>
      
      <div className="w-full max-w-md bg-gray-50 p-4 rounded-lg">
        <div className="flex items-start mb-3">
          <div className="min-w-[24px] h-6 flex items-center justify-center">
            <div className="w-3 h-3 rounded-full bg-black"></div>
          </div>
          <div className="ml-2">
            <p className="text-sm text-gray-500">From</p>
            <p className="font-medium">{location}</p>
          </div>
        </div>
        <div className="flex items-start">
          <div className="min-w-[24px] h-6 flex items-center justify-center">
            <div className="w-3 h-3 rounded-full bg-black border-2 border-white shadow"></div>
          </div>
          <div className="ml-2">
            <p className="text-sm text-gray-500">To</p>
            <p className="font-medium">{destination}</p>
          </div>
        </div>
      </div>
      
      <motion.button
        className="mt-6 py-3 px-6 border border-gray-200 rounded-md text-black bg-white hover:bg-gray-50"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        Cancel Request
      </motion.button>
      
      <motion.div 
        className="text-xs text-gray-500 mt-auto pt-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 2 }}
      >
        Tip: Drivers typically arrive within 5 minutes
      </motion.div>
    </div>
  );
}
