import { motion } from 'framer-motion';

export default function Header({ onProfileClick }) {
  return (
    <header className="bg-black text-white py-4 px-6 flex justify-between items-center z-10">
      <h1 className="text-xl font-[head] font-bold">RideUp</h1>
      <motion.div 
        className="h-8 w-8 font-[body] rounded-full bg-white text-black flex items-center justify-center text-sm font-medium cursor-pointer profile-icon hover:bg-gray-100 transition-colors duration-200"
        onClick={onProfileClick}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
      >
        JD
      </motion.div>
    </header>
  );
}
