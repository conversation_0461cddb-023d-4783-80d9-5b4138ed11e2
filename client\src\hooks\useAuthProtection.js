'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';

// Hook to protect user routes
export const useUserProtection = () => {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push('/user/login');
    }
  }, [user, loading, router]);

  return { user, loading };
};

// Hook to protect captain routes
export const useCaptainProtection = () => {
  const { captain, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !captain) {
      router.push('/captain/login');
    }
  }, [captain, loading, router]);

  return { captain, loading };
};
