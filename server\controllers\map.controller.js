const mapService = require('../services/map.service');
const { validationResult } = require('express-validator');

// Get coordinates from address
module.exports.getCoordinates = async (req, res) => {
  try {
    const { address } = req.query;
    
    if (!address) {
      return res.status(400).json({ message: 'Address is required' });
    }

    const coordinates = await mapService.getCoordinatesFromAddress(address);
    res.status(200).json({
      success: true,
      data: coordinates
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};

// Get address from coordinates
module.exports.getAddress = async (req, res) => {
  try {
    const { lat, lng } = req.query;
    
    const validation = mapService.validateCoordinates(lat, lng);
    if (!validation.valid) {
      return res.status(400).json({ message: validation.error });
    }

    const address = await mapService.getAddressFromCoordinates(validation.lat, validation.lng);
    res.status(200).json({
      success: true,
      data: address
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};

// Get route between two points
module.exports.getRoute = async (req, res) => {
  try {
    const { origin, destination, mode } = req.query;
    
    if (!origin || !destination) {
      return res.status(400).json({ message: 'Origin and destination are required' });
    }

    const route = await mapService.getRoute(origin, destination, mode);
    res.status(200).json({
      success: true,
      data: route
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};

// Get autocomplete suggestions
module.exports.getAutocompleteSuggestions = async (req, res) => {
  try {
    const { input, lat, lng, radius } = req.query;
    
    if (!input) {
      return res.status(400).json({ message: 'Input is required' });
    }

    let location = null;
    if (lat && lng) {
      const validation = mapService.validateCoordinates(lat, lng);
      if (validation.valid) {
        location = { lat: validation.lat, lng: validation.lng };
      }
    }

    const suggestions = await mapService.getAutocompleteSuggestions(input, location, radius);
    res.status(200).json({
      success: true,
      data: suggestions
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};

// Get place details
module.exports.getPlaceDetails = async (req, res) => {
  try {
    const { placeId } = req.params;
    
    if (!placeId) {
      return res.status(400).json({ message: 'Place ID is required' });
    }

    const placeDetails = await mapService.getPlaceDetails(placeId);
    res.status(200).json({
      success: true,
      data: placeDetails
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};

// Calculate distance between two points
module.exports.calculateDistance = async (req, res) => {
  try {
    const { lat1, lng1, lat2, lng2 } = req.query;
    
    const validation1 = mapService.validateCoordinates(lat1, lng1);
    const validation2 = mapService.validateCoordinates(lat2, lng2);
    
    if (!validation1.valid) {
      return res.status(400).json({ message: `Point 1: ${validation1.error}` });
    }
    
    if (!validation2.valid) {
      return res.status(400).json({ message: `Point 2: ${validation2.error}` });
    }

    const distance = mapService.calculateDistance(
      validation1.lat, validation1.lng,
      validation2.lat, validation2.lng
    );
    
    res.status(200).json({
      success: true,
      data: distance
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};

// Find nearby captains
module.exports.findNearbyCaptains = async (req, res) => {
  try {
    const { lat, lng, radius, status } = req.query;
    
    const validation = mapService.validateCoordinates(lat, lng);
    if (!validation.valid) {
      return res.status(400).json({ message: validation.error });
    }

    const captains = await mapService.findNearbyCaptains(
      validation.lat, 
      validation.lng, 
      radius ? parseFloat(radius) : 5,
      status || 'online'
    );
    
    res.status(200).json({
      success: true,
      data: captains,
      count: captains.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};

// Calculate estimated fare
module.exports.calculateFare = async (req, res) => {
  try {
    const { distance, duration, vehicleType } = req.query;
    
    if (!distance || !duration) {
      return res.status(400).json({ message: 'Distance and duration are required' });
    }

    const fare = mapService.calculateEstimatedFare(
      parseFloat(distance),
      parseFloat(duration),
      vehicleType
    );
    
    res.status(200).json({
      success: true,
      data: fare
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};

// Get traffic information
module.exports.getTrafficInfo = async (req, res) => {
  try {
    const { origin, destination } = req.query;
    
    if (!origin || !destination) {
      return res.status(400).json({ message: 'Origin and destination are required' });
    }

    const trafficInfo = await mapService.getTrafficInfo(origin, destination);
    res.status(200).json({
      success: true,
      data: trafficInfo
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};

// Get static map URL
module.exports.getStaticMapUrl = async (req, res) => {
  try {
    const { center, zoom, size, markers } = req.query;
    
    if (!center) {
      return res.status(400).json({ message: 'Center coordinates are required' });
    }

    let parsedMarkers = [];
    if (markers) {
      try {
        parsedMarkers = JSON.parse(markers);
      } catch (e) {
        return res.status(400).json({ message: 'Invalid markers format' });
      }
    }

    const mapUrl = mapService.getStaticMapUrl(center, zoom, size, parsedMarkers);
    res.status(200).json({
      success: true,
      data: { url: mapUrl }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};
