const rideModel = require('../models/ride.model');
const captainModel = require('../models/captain.model');
const mapService = require('../services/map.service');
const { validationResult } = require('express-validator');

// Create a new ride request
module.exports.createRide = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { pickup, destination, vehicleType } = req.body;
    const userId = req.user._id;

    // Get coordinates for pickup and destination if not provided
    let pickupCoords = pickup.coordinates;
    let destinationCoords = destination.coordinates;

    if (!pickupCoords && pickup.address) {
      const pickupLocation = await mapService.getCoordinatesFromAddress(pickup.address);
      pickupCoords = { lat: pickupLocation.lat, lng: pickupLocation.lng };
    }

    if (!destinationCoords && destination.address) {
      const destLocation = await mapService.getCoordinatesFromAddress(destination.address);
      destinationCoords = { lat: destLocation.lat, lng: destLocation.lng };
    }

    // Get route information
    const route = await mapService.getRoute(
      `${pickupCoords.lat},${pickupCoords.lng}`,
      `${destinationCoords.lat},${destinationCoords.lng}`
    );

    // Calculate fare
    const fare = mapService.calculateEstimatedFare(
      route.distance.value,
      route.duration.value,
      vehicleType
    );

    // Create ride
    const ride = new rideModel({
      user: userId,
      pickup: {
        address: pickup.address,
        coordinates: pickupCoords
      },
      destination: {
        address: destination.address,
        coordinates: destinationCoords
      },
      vehicleType: vehicleType,
      fare: fare,
      distance: route.distance,
      duration: route.duration,
      route: {
        overview_polyline: route.overview_polyline,
        bounds: route.bounds,
        steps: route.steps
      }
    });

    // Generate OTP
    ride.generateOTP();

    await ride.save();

    res.status(201).json({
      success: true,
      message: 'Ride created successfully',
      data: ride
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};

// Get nearby captains for a ride
module.exports.findNearbyCaptains = async (req, res) => {
  try {
    const { rideId } = req.params;
    const { radius = 5 } = req.query;

    const ride = await rideModel.findById(rideId);
    if (!ride) {
      return res.status(404).json({ message: 'Ride not found' });
    }

    // Find nearby captains
    const captains = await mapService.findNearbyCaptains(
      ride.pickup.coordinates.lat,
      ride.pickup.coordinates.lng,
      parseFloat(radius),
      'online'
    );

    // Filter captains by vehicle type
    const availableCaptains = captains.filter(captain => 
      captain.vehicle.vehicleType === ride.vehicleType
    );

    res.status(200).json({
      success: true,
      data: availableCaptains,
      count: availableCaptains.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};

// Captain accepts a ride
module.exports.acceptRide = async (req, res) => {
  try {
    const { rideId } = req.params;
    const captainId = req.captain._id;

    const ride = await rideModel.findById(rideId);
    if (!ride) {
      return res.status(404).json({ message: 'Ride not found' });
    }

    if (ride.status !== 'pending') {
      return res.status(400).json({ message: 'Ride is not available' });
    }

    // Update ride with captain and status
    ride.captain = captainId;
    await ride.updateStatus('accepted');

    // Update captain status to busy
    await captainModel.findByIdAndUpdate(captainId, { status: 'busy' });

    const populatedRide = await rideModel.findById(rideId)
      .populate('user', 'fullName email phoneNumber')
      .populate('captain', 'fullName email phoneNumber vehicle');

    res.status(200).json({
      success: true,
      message: 'Ride accepted successfully',
      data: populatedRide
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};

// Start a ride
module.exports.startRide = async (req, res) => {
  try {
    const { rideId } = req.params;
    const { otp } = req.body;
    const captainId = req.captain._id;

    const ride = await rideModel.findById(rideId);
    if (!ride) {
      return res.status(404).json({ message: 'Ride not found' });
    }

    if (ride.captain.toString() !== captainId.toString()) {
      return res.status(403).json({ message: 'Unauthorized' });
    }

    if (ride.status !== 'accepted') {
      return res.status(400).json({ message: 'Ride cannot be started' });
    }

    if (ride.otp !== otp) {
      return res.status(400).json({ message: 'Invalid OTP' });
    }

    await ride.updateStatus('ongoing');

    res.status(200).json({
      success: true,
      message: 'Ride started successfully',
      data: ride
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};

// Complete a ride
module.exports.completeRide = async (req, res) => {
  try {
    const { rideId } = req.params;
    const captainId = req.captain._id;

    const ride = await rideModel.findById(rideId);
    if (!ride) {
      return res.status(404).json({ message: 'Ride not found' });
    }

    if (ride.captain.toString() !== captainId.toString()) {
      return res.status(403).json({ message: 'Unauthorized' });
    }

    if (ride.status !== 'ongoing') {
      return res.status(400).json({ message: 'Ride is not ongoing' });
    }

    await ride.updateStatus('completed');

    // Update captain status back to online
    await captainModel.findByIdAndUpdate(captainId, { status: 'online' });

    res.status(200).json({
      success: true,
      message: 'Ride completed successfully',
      data: ride
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};

// Cancel a ride
module.exports.cancelRide = async (req, res) => {
  try {
    const { rideId } = req.params;
    const { reason } = req.body;
    const userId = req.user ? req.user._id : null;
    const captainId = req.captain ? req.captain._id : null;

    const ride = await rideModel.findById(rideId);
    if (!ride) {
      return res.status(404).json({ message: 'Ride not found' });
    }

    if (ride.status === 'completed' || ride.status === 'cancelled') {
      return res.status(400).json({ message: 'Ride cannot be cancelled' });
    }

    // Determine who is cancelling
    let cancelledBy = 'system';
    if (userId && ride.user.toString() === userId.toString()) {
      cancelledBy = 'user';
    } else if (captainId && ride.captain && ride.captain.toString() === captainId.toString()) {
      cancelledBy = 'captain';
    }

    ride.cancellationReason = reason;
    ride.cancelledBy = cancelledBy;
    await ride.updateStatus('cancelled');

    // If captain was assigned, update their status back to online
    if (ride.captain) {
      await captainModel.findByIdAndUpdate(ride.captain, { status: 'online' });
    }

    res.status(200).json({
      success: true,
      message: 'Ride cancelled successfully',
      data: ride
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};

// Get ride details
module.exports.getRideDetails = async (req, res) => {
  try {
    const { rideId } = req.params;

    const ride = await rideModel.findById(rideId)
      .populate('user', 'fullName email phoneNumber')
      .populate('captain', 'fullName email phoneNumber vehicle location');

    if (!ride) {
      return res.status(404).json({ message: 'Ride not found' });
    }

    res.status(200).json({
      success: true,
      data: ride
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};
