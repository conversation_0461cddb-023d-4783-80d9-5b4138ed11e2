// API service functions for RideUp

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

// Helper function for handling API responses
const handleResponse = async (response) => {
  const data = await response.json();
  
  if (!response.ok) {
    // If the response contains validation errors, format them
    if (data.errors && Array.isArray(data.errors)) {
      const errorMessage = data.errors.map(err => `${err.msg}`).join(', ');
      throw new Error(errorMessage);
    }
    
    // Otherwise use the error message or default
    const errorMessage = data.message || data.error || 'Something went wrong';
    throw new Error(errorMessage);
  }
  
  return data;
};

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('token');
  return {
    'Content-Type': 'application/json',
    'Authorization': token ? `Bearer ${token}` : '',
  };
};

// User API functions

// Register a new user
export const registerUser = async (userData) => {
  const response = await fetch(`${API_URL}/users/register`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(userData),
  });
  
  return handleResponse(response);
};

// Login user
export const loginUser = async ({ email, password }) => {
  const response = await fetch(`${API_URL}/users/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email, password }),
  });
  
  return handleResponse(response);
};

// Get user profile
export const getUserProfile = async () => {
  const response = await fetch(`${API_URL}/users/profile`, {
    method: 'GET',
    headers: getAuthHeaders(),
  });
  
  return handleResponse(response);
};

// Logout user
export const logoutUser = async () => {
  const response = await fetch(`${API_URL}/users/logout`, {
    method: 'GET',
    headers: getAuthHeaders(),
  });
  
  return handleResponse(response);
};

// Captain API functions

// Register a new captain
export const registerCaptain = async (captainData) => {
  const response = await fetch(`${API_URL}/captains/register`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(captainData),
  });
  
  return handleResponse(response);
};

// Login captain
export const loginCaptain = async ({ email, password }) => {
  const response = await fetch(`${API_URL}/captains/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email, password }),
  });
  
  return handleResponse(response);
};

// Get captain profile
export const getCaptainProfile = async () => {
  const response = await fetch(`${API_URL}/captains/profile`, {
    method: 'GET',
    headers: getAuthHeaders(),
  });
  
  return handleResponse(response);
};

// Logout captain
export const logoutCaptain = async () => {
  const response = await fetch(`${API_URL}/captains/logout`, {
    method: 'GET',
    headers: getAuthHeaders(),
  });
  
  return handleResponse(response);
};

// Update captain profile
export const updateCaptainProfile = async (profileData) => {
  const response = await fetch(`${API_URL}/captains/profile`, {
    method: 'PUT',
    headers: getAuthHeaders(),
    body: JSON.stringify(profileData),
  });
  
  return handleResponse(response);
};

// Update captain location
export const updateCaptainLocation = async (location) => {
  const response = await fetch(`${API_URL}/captains/location`, {
    method: 'PUT',
    headers: getAuthHeaders(),
    body: JSON.stringify({ location }),
  });
  
  return handleResponse(response);
};

// Update captain status
export const updateCaptainStatus = async (status) => {
  const response = await fetch(`${API_URL}/captains/status`, {
    method: 'PUT',
    headers: getAuthHeaders(),
    body: JSON.stringify({ status }),
  });
  
  return handleResponse(response);
};

// Update captain socket ID
export const updateCaptainSocketId = async (socketId) => {
  const response = await fetch(`${API_URL}/captains/socket`, {
    method: 'PUT',
    headers: getAuthHeaders(),
    body: JSON.stringify({ socketId }),
  });
  
  return handleResponse(response);
};
