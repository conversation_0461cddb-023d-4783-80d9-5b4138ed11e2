import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

export default function RazorpayCheckout({ amount, onPaymentComplete, onCancel, rideDetails }) {
  const [isLoading, setIsLoading] = useState(true);
  const [showPaymentSuccess, setShowPaymentSuccess] = useState(false);

  // Mock the Razorpay integration
  useEffect(() => {
    // Simulate loading the Razorpay SDK
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  const handlePayNow = () => {
    // In a real implementation, you would initialize Razorpay here
    setIsLoading(true);
    
    // Mock payment success after delay
    setTimeout(() => {
      setIsLoading(false);
      setShowPaymentSuccess(true);
      
      // Auto continue after showing success message
      setTimeout(() => {
        onPaymentComplete({
          transactionId: 'rzp_' + Math.random().toString(36).substr(2, 9),
          amount: amount / 100,
          status: 'success'
        });
      }, 2000);
    }, 2000);
  };

  return (
    <div className="h-full flex flex-col">
      <motion.button
        className="mb-6 flex items-center text-gray-600 hover:text-black"
        onClick={onCancel}
        initial={{ x: -10, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        whileHover={{ x: -3 }}
        disabled={isLoading || showPaymentSuccess}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
        </svg>
        Back to ride selection
      </motion.button>

      <h2 className="text-2xl font-bold text-black mb-6">Complete Payment</h2>
      
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <div className="flex justify-between mb-2">
          <span className="text-gray-600">From</span>
          <span className="font-medium">{rideDetails.from}</span>
        </div>
        <div className="flex justify-between mb-2">
          <span className="text-gray-600">To</span>
          <span className="font-medium">{rideDetails.to}</span>
        </div>
        <div className="flex justify-between mb-2">
          <span className="text-gray-600">Vehicle</span>
          <span className="font-medium capitalize">{rideDetails.vehicleType}</span>
        </div>
        <div className="flex justify-between pt-2 border-t border-gray-200 mt-2">
          <span className="font-medium">Total Amount</span>
          <span className="font-bold">${(amount / 100).toFixed(2)}</span>
        </div>
      </div>

      {showPaymentSuccess ? (
        <motion.div 
          className="flex flex-col items-center justify-center p-6 bg-gray-50 rounded-lg"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ type: "spring", stiffness: 400, damping: 15 }}
        >
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h3 className="text-xl font-medium mb-2">Payment Successful!</h3>
          <p className="text-gray-600 text-center">
            Your payment has been processed successfully.<br/>
            Looking for drivers...
          </p>
        </motion.div>
      ) : (
        <>
          <div className="bg-white border border-gray-200 rounded-lg p-5 mb-6">
            <h3 className="text-lg font-medium mb-4">Payment Methods</h3>
            
            <div className="space-y-3">
              <div className="flex items-center p-3 border border-gray-200 rounded-md">
                <input
                  id="card"
                  name="paymentMethod"
                  type="radio"
                  checked
                  className="h-4 w-4 text-black border-gray-300"
                />
                <label htmlFor="card" className="ml-3 flex items-center">
                  <span className="font-medium mr-2">Credit/Debit Card</span>
                  <div className="flex space-x-1">
                    <div className="w-8 h-5 bg-gray-200 rounded"></div>
                    <div className="w-8 h-5 bg-gray-200 rounded"></div>
                    <div className="w-8 h-5 bg-gray-200 rounded"></div>
                  </div>
                </label>
              </div>
              
              <div className="flex items-center p-3 border border-gray-200 rounded-md bg-gray-50">
                <input
                  id="upi"
                  name="paymentMethod"
                  type="radio"
                  className="h-4 w-4 text-black border-gray-300"
                />
                <label htmlFor="upi" className="ml-3 block">
                  <span className="font-medium">UPI</span>
                </label>
              </div>
              
              <div className="flex items-center p-3 border border-gray-200 rounded-md bg-gray-50">
                <input
                  id="wallet"
                  name="paymentMethod"
                  type="radio"
                  className="h-4 w-4 text-black border-gray-300"
                />
                <label htmlFor="wallet" className="ml-3 block">
                  <span className="font-medium">Wallet</span>
                </label>
              </div>
            </div>
          </div>
          
          {isLoading ? (
            <div className="flex items-center justify-center p-4">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-black"></div>
              <span className="ml-3">Processing payment...</span>
            </div>
          ) : (
            <motion.button
              className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md bg-black text-white text-lg font-medium hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200"
              onClick={handlePayNow}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Pay Now
            </motion.button>
          )}
        </>
      )}
      
      <div className="text-xs text-gray-500 text-center mt-auto pt-4">
        Secured by Razorpay <span className="text-black mx-1">•</span> 256-bit encryption
      </div>
    </div>
  );
}
