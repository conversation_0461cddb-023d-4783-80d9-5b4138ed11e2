const express = require('express');
const router = express.Router();
const { query, param } = require('express-validator');
const mapController = require('../controllers/map.controller');
const authMiddleware = require('../middlewares/auth.middleware');

// Get coordinates from address
router.get('/coordinates', [
  query('address').notEmpty().withMessage('Address is required')
], mapController.getCoordinates);

// Get address from coordinates
router.get('/address', [
  query('lat').isFloat({ min: -90, max: 90 }).withMessage('Valid latitude is required'),
  query('lng').isFloat({ min: -180, max: 180 }).withMessage('Valid longitude is required')
], mapController.getAddress);

// Get route between two points
router.get('/route', [
  query('origin').notEmpty().withMessage('Origin is required'),
  query('destination').notEmpty().withMessage('Destination is required'),
  query('mode').optional().isIn(['driving', 'walking', 'bicycling', 'transit']).withMessage('Invalid travel mode')
], mapController.getRoute);

// Get autocomplete suggestions
router.get('/autocomplete', [
  query('input').notEmpty().withMessage('Input is required'),
  query('lat').optional().isFloat({ min: -90, max: 90 }).withMessage('Valid latitude required'),
  query('lng').optional().isFloat({ min: -180, max: 180 }).withMessage('Valid longitude required'),
  query('radius').optional().isInt({ min: 1, max: 50000 }).withMessage('Radius must be between 1 and 50000 meters')
], mapController.getAutocompleteSuggestions);

// Get place details
router.get('/place/:placeId', [
  param('placeId').notEmpty().withMessage('Place ID is required')
], mapController.getPlaceDetails);

// Calculate distance between two points
router.get('/distance', [
  query('lat1').isFloat({ min: -90, max: 90 }).withMessage('Valid latitude 1 is required'),
  query('lng1').isFloat({ min: -180, max: 180 }).withMessage('Valid longitude 1 is required'),
  query('lat2').isFloat({ min: -90, max: 90 }).withMessage('Valid latitude 2 is required'),
  query('lng2').isFloat({ min: -180, max: 180 }).withMessage('Valid longitude 2 is required')
], mapController.calculateDistance);

// Find nearby captains (requires authentication)
router.get('/nearby-captains', [
  query('lat').isFloat({ min: -90, max: 90 }).withMessage('Valid latitude is required'),
  query('lng').isFloat({ min: -180, max: 180 }).withMessage('Valid longitude is required'),
  query('radius').optional().isFloat({ min: 0.1, max: 50 }).withMessage('Radius must be between 0.1 and 50 km'),
  query('status').optional().isIn(['online', 'offline', 'busy']).withMessage('Invalid status')
], authMiddleware.authUser, mapController.findNearbyCaptains);

// Calculate estimated fare
router.get('/fare', [
  query('distance').isFloat({ min: 0 }).withMessage('Valid distance in meters is required'),
  query('duration').isFloat({ min: 0 }).withMessage('Valid duration in seconds is required'),
  query('vehicleType').optional().isIn(['sedan', 'suv', 'van', 'car', 'motorcycle', 'bicycle', 'auto']).withMessage('Invalid vehicle type')
], mapController.calculateFare);

// Get traffic information
router.get('/traffic', [
  query('origin').notEmpty().withMessage('Origin is required'),
  query('destination').notEmpty().withMessage('Destination is required')
], mapController.getTrafficInfo);

// Get static map URL
router.get('/static-map', [
  query('center').notEmpty().withMessage('Center coordinates are required'),
  query('zoom').optional().isInt({ min: 1, max: 20 }).withMessage('Zoom must be between 1 and 20'),
  query('size').optional().matches(/^\d+x\d+$/).withMessage('Size must be in format WIDTHxHEIGHT'),
  query('markers').optional().isJSON().withMessage('Markers must be valid JSON')
], mapController.getStaticMapUrl);

module.exports = router;
