import { useState } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';

export default function ConfirmedRide({ rideDetails }) {
  const [activeTab, setActiveTab] = useState('ride');
  const router = useRouter();
  
  // Animation variants
  const tabVariants = {
    inactive: { 
      color: "#6b7280",
      borderBottom: "2px solid transparent"
    },
    active: { 
      color: "#000000", 
      borderBottom: "2px solid #000000"
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { staggerChildren: 0.1 }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 }
  };
  
  const handleDoneClick = () => {
    // Navigate to the tracking page
    router.push(`/in/en/ride/${rideDetails.ride.id}/confirmed/${rideDetails.ride.id}`);
  };
  
  return (
    <div className="h-full font-[body] relative flex flex-col">
      <header className="bg-black text-white py-4 px-6 flex items-center">
        <button className="mr-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h1 className="text-xl font-bold">Your Ride</h1>
      </header>
      
      <motion.div 
        className="bg-gray-100 p-4"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div className="flex items-center space-x-4 mb-4" variants={itemVariants}>
          <div className="relative">
            <div className="w-16 h-16 rounded-full overflow-hidden">
              <img 
                src={rideDetails.driver.photo} 
                alt={rideDetails.driver.name} 
                className="w-full h-full object-cover"
              />
            </div>
            <div className="absolute bottom-0 right-0 bg-green-500 w-4 h-4 rounded-full border-2 border-white"></div>
          </div>
          <div>
            <h2 className="text-lg font-bold">{rideDetails.driver.name}</h2>
            <div className="flex items-center space-x-2">
              <div className="flex">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ${i < Math.floor(rideDetails.driver.rating) ? 'text-yellow-500' : 'text-gray-300'}`} viewBox="0 0 20 20" fill="currentColor">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <span className="text-sm">{rideDetails.driver.rating}</span>
            </div>
          </div>
          <div className="ml-auto flex space-x-3">
            <motion.button 
              className="p-3 bg-white rounded-full shadow-sm"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-800" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
            </motion.button>
            <motion.button 
              className="p-3 bg-white rounded-full shadow-sm"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-800" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </motion.button>
          </div>
        </motion.div>
        
        <motion.div className="bg-white p-4 rounded-lg shadow-sm mb-4" variants={itemVariants}>
          <div className="flex items-center space-x-3 mb-2">
            <div className="p-2 bg-gray-100 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <p className="font-medium text-gray-800">{rideDetails.vehicle.model} • {rideDetails.vehicle.color}</p>
              <p className="text-sm text-gray-600">{rideDetails.vehicle.plate}</p>
            </div>
          </div>
        </motion.div>
      </motion.div>
      
      <div className="flex border-b border-gray-200">
        <motion.button
          className="flex-1 py-4 text-center font-medium text-sm"
          variants={tabVariants}
          animate={activeTab === 'ride' ? 'active' : 'inactive'}
          onClick={() => setActiveTab('ride')}
          whileHover={{ backgroundColor: "#f9fafb" }}
        >
          Ride Details
        </motion.button>
        <motion.button
          className="flex-1 py-4 text-center font-medium text-sm"
          variants={tabVariants}
          animate={activeTab === 'receipt' ? 'active' : 'inactive'}
          onClick={() => setActiveTab('receipt')}
          whileHover={{ backgroundColor: "#f9fafb" }}
        >
          Receipt
        </motion.button>
      </div>
      
      <div className="flex-grow p-6 overflow-y-auto">
        <AnimatedTabContent activeTab={activeTab} rideDetails={rideDetails} />
      </div>

      <motion.div 
        className="p-4 border-t border-gray-200"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        <button 
          className="w-full py-3 px-4 bg-black text-white rounded-md font-medium hover:bg-gray-900 transition-colors"
          onClick={handleDoneClick}
        >
          Done
        </button>
      </motion.div>
    </div>
  );
}

function AnimatedTabContent({ activeTab, rideDetails }) {
  const variants = {
    hidden: { opacity: 0, x: -20 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: { 
        staggerChildren: 0.1,
        duration: 0.3 
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 }
  };

  if (activeTab === 'ride') {
    return (
      <motion.div
        variants={variants}
        initial="hidden"
        animate="visible"
      >
        <motion.div className="mb-8" variants={itemVariants}>
          <h3 className="text-sm font-medium text-gray-500 mb-3">ROUTE</h3>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-start mb-3">
              <div className="min-w-[24px] h-full flex flex-col items-center">
                <div className="w-3 h-3 rounded-full bg-black"></div>
                <div className="w-0.5 h-full bg-gray-300 my-1 flex-grow"></div>
              </div>
              <div className="ml-2">
                <p className="text-sm text-gray-500">Pickup Location</p>
                <p className="font-medium">123 Main St, Brooklyn, NY</p>
                <p className="text-xs text-gray-500 mt-1">12:30 PM</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="min-w-[24px] h-6 flex items-center justify-center">
                <div className="w-3 h-3 rounded-full border-2 border-black bg-white"></div>
              </div>
              <div className="ml-2">
                <p className="text-sm text-gray-500">Drop-off Location</p>
                <p className="font-medium">456 Park Ave, Manhattan, NY</p>
                <p className="text-xs text-gray-500 mt-1">12:45 PM (Estimated)</p>
              </div>
            </div>
          </div>
        </motion.div>
        
        <motion.div className="mb-6" variants={itemVariants}>
          <h3 className="text-sm font-medium text-gray-500 mb-3">TRIP</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Distance</span>
              <span className="font-medium">{rideDetails.ride.distance}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Duration</span>
              <span className="font-medium">{rideDetails.ride.duration}</span>
            </div>
          </div>
        </motion.div>
        
        <motion.div variants={itemVariants}>
          <h3 className="text-sm font-medium text-gray-500 mb-3">PAYMENT</h3>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex justify-between items-center">
              <span className="font-medium">Fare</span>
              <span className="font-bold">{rideDetails.ride.fare}</span>
            </div>
          </div>
        </motion.div>
      </motion.div>
    );
  }
  
  if (activeTab === 'receipt') {
    return (
      <motion.div
        variants={variants}
        initial="hidden"
        animate="visible"
      >
        <motion.div className="text-center mb-6" variants={itemVariants}>
          <h2 className="text-2xl font-bold">Receipt</h2>
          <p className="text-gray-600">Ride #{rideDetails.ride.id}</p>
        </motion.div>
        
        <motion.div className="border-t border-b border-gray-200 py-4 mb-4" variants={itemVariants}>
          <div className="flex justify-between mb-2">
            <span className="text-gray-600">Base Fare</span>
            <span>$12.00</span>
          </div>
          <div className="flex justify-between mb-2">
            <span className="text-gray-600">Distance (5.7 mi)</span>
            <span>$2.50</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Subtotal</span>
            <span>$14.50</span>
          </div>
        </motion.div>
        
        <motion.div className="flex justify-between font-bold text-lg" variants={itemVariants}>
          <span>Total</span>
          <span>$14.50</span>
        </motion.div>
      </motion.div>
    );
  }
  
  return null;
}
