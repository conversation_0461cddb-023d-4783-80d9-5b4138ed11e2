const axios = require('axios');

// Get coordinates from address using Google Geocoding API
module.exports.getCoordinatesFromAddress = async (address) => {
  const apiKey = process.env.GOOGLE_MAPS_API_KEY;

  if (!apiKey) {
    throw new Error('Google Maps API key not configured');
  }

  try {
    const response = await axios.get('https://maps.googleapis.com/maps/api/geocode/json', {
      params: {
        address: address,
        key: apiKey
      }
    });

    if (response.data.status === 'OK' && response.data.results.length > 0) {
      const location = response.data.results[0].geometry.location;
      return {
        lat: location.lat,
        lng: location.lng,
        formatted_address: response.data.results[0].formatted_address
      };
    } else {
      throw new Error('Address not found');
    }
  } catch (error) {
    throw new Error(`Geocoding error: ${error.message}`);
  }
};

// Get address from coordinates using Google Reverse Geocoding API
module.exports.getAddressFromCoordinates = async (lat, lng) => {
  const apiKey = process.env.GOOGLE_MAPS_API_KEY;

  if (!apiKey) {
    throw new Error('Google Maps API key not configured');
  }

  try {
    const response = await axios.get('https://maps.googleapis.com/maps/api/geocode/json', {
      params: {
        latlng: `${lat},${lng}`,
        key: apiKey
      }
    });

    if (response.data.status === 'OK' && response.data.results.length > 0) {
      return {
        formatted_address: response.data.results[0].formatted_address,
        address_components: response.data.results[0].address_components
      };
    } else {
      throw new Error('Coordinates not found');
    }
  } catch (error) {
    throw new Error(`Reverse geocoding error: ${error.message}`);
  }
};

// Get route information between two points using Google Directions API
module.exports.getRoute = async (origin, destination, mode = 'driving') => {
  const apiKey = process.env.GOOGLE_MAPS_API_KEY;

  if (!apiKey) {
    throw new Error('Google Maps API key not configured');
  }

  try {
    const response = await axios.get('https://maps.googleapis.com/maps/api/directions/json', {
      params: {
        origin: origin,
        destination: destination,
        mode: mode, // driving, walking, bicycling, transit
        key: apiKey,
        alternatives: true, // Get alternative routes
        traffic_model: 'best_guess', // For real-time traffic
        departure_time: 'now'
      }
    });

    if (response.data.status === 'OK' && response.data.routes.length > 0) {
      const route = response.data.routes[0];
      const leg = route.legs[0];

      return {
        distance: {
          text: leg.distance.text,
          value: leg.distance.value // in meters
        },
        duration: {
          text: leg.duration.text,
          value: leg.duration.value // in seconds
        },
        duration_in_traffic: leg.duration_in_traffic ? {
          text: leg.duration_in_traffic.text,
          value: leg.duration_in_traffic.value
        } : null,
        start_location: leg.start_location,
        end_location: leg.end_location,
        start_address: leg.start_address,
        end_address: leg.end_address,
        steps: leg.steps.map(step => ({
          distance: step.distance,
          duration: step.duration,
          instructions: step.html_instructions.replace(/<[^>]*>/g, ''), // Remove HTML tags
          maneuver: step.maneuver,
          start_location: step.start_location,
          end_location: step.end_location
        })),
        overview_polyline: route.overview_polyline.points,
        bounds: route.bounds,
        warnings: route.warnings,
        waypoint_order: route.waypoint_order
      };
    } else {
      throw new Error(`Route not found: ${response.data.status}`);
    }
  } catch (error) {
    throw new Error(`Directions error: ${error.message}`);
  }
};

// Get autocomplete suggestions for places using Google Places API
module.exports.getAutocompleteSuggestions = async (input, location = null, radius = 50000) => {
  const apiKey = process.env.GOOGLE_MAPS_API_KEY;

  if (!apiKey) {
    throw new Error('Google Maps API key not configured');
  }

  try {
    const params = {
      input: input,
      key: apiKey,
      types: 'establishment|geocode', // Include both places and addresses
    };

    // Add location bias if provided
    if (location) {
      params.location = `${location.lat},${location.lng}`;
      params.radius = radius;
    }

    const response = await axios.get('https://maps.googleapis.com/maps/api/place/autocomplete/json', {
      params: params
    });

    if (response.data.status === 'OK') {
      return response.data.predictions.map(prediction => ({
        place_id: prediction.place_id,
        description: prediction.description,
        main_text: prediction.structured_formatting.main_text,
        secondary_text: prediction.structured_formatting.secondary_text,
        types: prediction.types
      }));
    } else {
      return [];
    }
  } catch (error) {
    throw new Error(`Autocomplete error: ${error.message}`);
  }
};

// Get place details using Google Places API
module.exports.getPlaceDetails = async (placeId) => {
  const apiKey = process.env.GOOGLE_MAPS_API_KEY;

  if (!apiKey) {
    throw new Error('Google Maps API key not configured');
  }

  try {
    const response = await axios.get('https://maps.googleapis.com/maps/api/place/details/json', {
      params: {
        place_id: placeId,
        key: apiKey,
        fields: 'name,formatted_address,geometry,place_id,types,rating,opening_hours'
      }
    });

    if (response.data.status === 'OK') {
      const place = response.data.result;
      return {
        place_id: place.place_id,
        name: place.name,
        formatted_address: place.formatted_address,
        location: place.geometry.location,
        types: place.types,
        rating: place.rating,
        opening_hours: place.opening_hours
      };
    } else {
      throw new Error('Place not found');
    }
  } catch (error) {
    throw new Error(`Place details error: ${error.message}`);
  }
};

// Calculate distance between two points using Haversine formula
module.exports.calculateDistance = (lat1, lng1, lat2, lng2) => {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c; // Distance in kilometers

  return {
    kilometers: distance,
    miles: distance * 0.621371,
    meters: distance * 1000
  };
};

// Find nearby captains within a radius
module.exports.findNearbyCaptains = async (lat, lng, radius = 5, status = 'online') => {
  const captainModel = require('../models/captain.model');

  try {
    // Find captains within the specified radius using MongoDB geospatial query
    const captains = await captainModel.find({
      status: status,
      location: {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: [lng, lat] // Note: MongoDB uses [longitude, latitude]
          },
          $maxDistance: radius * 1000 // Convert km to meters
        }
      }
    }).select('-password');

    return captains;
  } catch (error) {
    // Fallback to manual distance calculation if geospatial index is not available
    const captains = await captainModel.find({
      status: status,
      'location.lat': { $exists: true },
      'location.lng': { $exists: true }
    }).select('-password');

    const nearbyCaptains = captains.filter(captain => {
      if (!captain.location || !captain.location.lat || !captain.location.lng) {
        return false;
      }

      const distance = module.exports.calculateDistance(
        lat, lng,
        captain.location.lat, captain.location.lng
      );

      return distance.kilometers <= radius;
    });

    return nearbyCaptains;
  }
};

// Get estimated fare based on distance and time
module.exports.calculateEstimatedFare = (distance, duration, vehicleType = 'sedan') => {
  // Base fare structure (in your currency)
  const fareStructure = {
    sedan: {
      baseFare: 50,
      perKm: 15,
      perMinute: 2,
      minimumFare: 80
    },
    suv: {
      baseFare: 70,
      perKm: 20,
      perMinute: 3,
      minimumFare: 120
    },
    van: {
      baseFare: 80,
      perKm: 25,
      perMinute: 3,
      minimumFare: 150
    },
    auto: {
      baseFare: 30,
      perKm: 10,
      perMinute: 1.5,
      minimumFare: 50
    },
    motorcycle: {
      baseFare: 25,
      perKm: 8,
      perMinute: 1,
      minimumFare: 40
    },
    bicycle: {
      baseFare: 20,
      perKm: 5,
      perMinute: 0.5,
      minimumFare: 30
    }
  };

  const rates = fareStructure[vehicleType] || fareStructure.sedan;
  const distanceInKm = distance / 1000; // Convert meters to kilometers
  const durationInMinutes = duration / 60; // Convert seconds to minutes

  const calculatedFare = rates.baseFare +
                        (distanceInKm * rates.perKm) +
                        (durationInMinutes * rates.perMinute);

  const finalFare = Math.max(calculatedFare, rates.minimumFare);

  return {
    baseFare: rates.baseFare,
    distanceFare: distanceInKm * rates.perKm,
    timeFare: durationInMinutes * rates.perMinute,
    totalFare: Math.round(finalFare),
    minimumFare: rates.minimumFare,
    breakdown: {
      distance: `${distanceInKm.toFixed(2)} km`,
      duration: `${Math.round(durationInMinutes)} min`,
      vehicleType: vehicleType
    }
  };
};

// Get traffic information for a route
module.exports.getTrafficInfo = async (origin, destination) => {
  const apiKey = process.env.GOOGLE_MAPS_API_KEY;

  if (!apiKey) {
    throw new Error('Google Maps API key not configured');
  }

  try {
    const response = await axios.get('https://maps.googleapis.com/maps/api/directions/json', {
      params: {
        origin: origin,
        destination: destination,
        key: apiKey,
        departure_time: 'now',
        traffic_model: 'best_guess'
      }
    });

    if (response.data.status === 'OK' && response.data.routes.length > 0) {
      const route = response.data.routes[0];
      const leg = route.legs[0];

      const normalDuration = leg.duration.value;
      const trafficDuration = leg.duration_in_traffic ? leg.duration_in_traffic.value : normalDuration;
      const delay = trafficDuration - normalDuration;

      let trafficLevel = 'light';
      if (delay > 300) trafficLevel = 'heavy'; // More than 5 minutes delay
      else if (delay > 120) trafficLevel = 'moderate'; // More than 2 minutes delay

      return {
        normal_duration: {
          text: leg.duration.text,
          value: normalDuration
        },
        traffic_duration: leg.duration_in_traffic ? {
          text: leg.duration_in_traffic.text,
          value: trafficDuration
        } : null,
        delay_seconds: delay,
        traffic_level: trafficLevel,
        delay_text: delay > 60 ? `${Math.round(delay/60)} min delay` : `${delay} sec delay`
      };
    } else {
      throw new Error('Traffic information not available');
    }
  } catch (error) {
    throw new Error(`Traffic info error: ${error.message}`);
  }
};

// Validate coordinates
module.exports.validateCoordinates = (lat, lng) => {
  const latitude = parseFloat(lat);
  const longitude = parseFloat(lng);

  if (isNaN(latitude) || isNaN(longitude)) {
    return { valid: false, error: 'Coordinates must be valid numbers' };
  }

  if (latitude < -90 || latitude > 90) {
    return { valid: false, error: 'Latitude must be between -90 and 90' };
  }

  if (longitude < -180 || longitude > 180) {
    return { valid: false, error: 'Longitude must be between -180 and 180' };
  }

  return { valid: true, lat: latitude, lng: longitude };
};

// Get static map URL for displaying map images
module.exports.getStaticMapUrl = (center, zoom = 15, size = '600x400', markers = []) => {
  const apiKey = process.env.GOOGLE_MAPS_API_KEY;

  if (!apiKey) {
    throw new Error('Google Maps API key not configured');
  }

  let url = `https://maps.googleapis.com/maps/api/staticmap?center=${center}&zoom=${zoom}&size=${size}&key=${apiKey}`;

  // Add markers if provided
  markers.forEach((marker, index) => {
    const color = marker.color || 'red';
    const label = marker.label || (index + 1).toString();
    url += `&markers=color:${color}|label:${label}|${marker.lat},${marker.lng}`;
  });

  return url;
};