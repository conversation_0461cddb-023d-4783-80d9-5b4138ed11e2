const mongoose = require('mongoose');

const rideSchema = new mongoose.Schema({
    user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    captain: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Captain'
    },
    pickup: {
        address: {
            type: String,
            required: true
        },
        coordinates: {
            lat: {
                type: Number,
                required: true
            },
            lng: {
                type: Number,
                required: true
            }
        }
    },
    destination: {
        address: {
            type: String,
            required: true
        },
        coordinates: {
            lat: {
                type: Number,
                required: true
            },
            lng: {
                type: Number,
                required: true
            }
        }
    },
    vehicleType: {
        type: String,
        required: true,
        enum: ['sedan', 'suv', 'van', 'car', 'motorcycle', 'bicycle', 'auto']
    },
    status: {
        type: String,
        default: 'pending',
        enum: ['pending', 'accepted', 'ongoing', 'completed', 'cancelled']
    },
    fare: {
        baseFare: {
            type: Number,
            required: true
        },
        distanceFare: {
            type: Number,
            required: true
        },
        timeFare: {
            type: Number,
            required: true
        },
        totalFare: {
            type: Number,
            required: true
        },
        currency: {
            type: String,
            default: 'INR'
        }
    },
    distance: {
        text: String,
        value: Number // in meters
    },
    duration: {
        text: String,
        value: Number // in seconds
    },
    route: {
        overview_polyline: String,
        bounds: Object,
        steps: Array
    },
    otp: {
        type: String,
        length: 4
    },
    paymentStatus: {
        type: String,
        default: 'pending',
        enum: ['pending', 'completed', 'failed', 'refunded']
    },
    paymentMethod: {
        type: String,
        enum: ['cash', 'card', 'upi', 'wallet']
    },
    rating: {
        userRating: {
            type: Number,
            min: 1,
            max: 5
        },
        captainRating: {
            type: Number,
            min: 1,
            max: 5
        },
        userComment: String,
        captainComment: String
    },
    timestamps: {
        requested: {
            type: Date,
            default: Date.now
        },
        accepted: Date,
        started: Date,
        completed: Date,
        cancelled: Date
    },
    cancellationReason: String,
    cancelledBy: {
        type: String,
        enum: ['user', 'captain', 'system']
    }
}, {
    timestamps: true
});

// Create indexes for better query performance
rideSchema.index({ user: 1, status: 1 });
rideSchema.index({ captain: 1, status: 1 });
rideSchema.index({ status: 1, createdAt: -1 });
rideSchema.index({ 'pickup.coordinates': '2dsphere' });
rideSchema.index({ 'destination.coordinates': '2dsphere' });

// Generate OTP for ride verification
rideSchema.methods.generateOTP = function() {
    const otp = Math.floor(1000 + Math.random() * 9000).toString();
    this.otp = otp;
    return otp;
};

// Calculate estimated arrival time
rideSchema.methods.calculateETA = function(captainLocation) {
    // This is a simplified calculation
    // In a real app, you'd use the Google Maps API for accurate ETA
    const distance = this.calculateDistance(
        captainLocation.lat, 
        captainLocation.lng,
        this.pickup.coordinates.lat,
        this.pickup.coordinates.lng
    );
    
    // Assume average speed of 30 km/h in city traffic
    const timeInHours = distance / 30;
    const timeInMinutes = Math.round(timeInHours * 60);
    
    return {
        minutes: timeInMinutes,
        text: `${timeInMinutes} min`
    };
};

// Calculate distance between two points (Haversine formula)
rideSchema.methods.calculateDistance = function(lat1, lng1, lat2, lng2) {
    const R = 6371; // Earth's radius in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = 
        Math.sin(dLat/2) * Math.sin(dLat/2) +
        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
        Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c; // Distance in kilometers
};

// Update ride status with timestamp
rideSchema.methods.updateStatus = function(newStatus) {
    this.status = newStatus;
    
    switch(newStatus) {
        case 'accepted':
            this.timestamps.accepted = new Date();
            break;
        case 'ongoing':
            this.timestamps.started = new Date();
            break;
        case 'completed':
            this.timestamps.completed = new Date();
            break;
        case 'cancelled':
            this.timestamps.cancelled = new Date();
            break;
    }
    
    return this.save();
};

const rideModel = mongoose.model('Ride', rideSchema);
module.exports = rideModel;
