"use client";

import { useState, useEffect, use } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';

export default function RideTrackingPage({ params }) {
  // Unwrap the params Promise
  const unwrappedParams = use(params);
  const id = unwrappedParams.id;
  const ride_id = unwrappedParams.ride_id;
  
  const [isMapExpanded, setIsMapExpanded] = useState(false);
  const [otpValue, setOtpValue] = useState('');
  const [eta, setEta] = useState('12 min');
  const [distance, setDistance] = useState('2.3 km');

  // Simulated driver location updates
  const [driverLocation, setDriverLocation] = useState({ lat: 40.7128, lng: -74.006 });
  
  useEffect(() => {
    // Simulate driver movement
    const interval = setInterval(() => {
      setDriverLocation(prev => ({
        lat: prev.lat + (Math.random() * 0.001 - 0.0005),
        lng: prev.lng + (Math.random() * 0.001 - 0.0005)
      }));
      
      // Update ETA
      const currentEta = parseInt(eta);
      if (currentEta > 1) {
        setEta(`${currentEta - 1} min`);
      }
      
      // Update distance
      const currentDistance = parseFloat(distance);
      if (currentDistance > 0.3) {
        setDistance(`${(currentDistance - 0.2).toFixed(1)} km`);
      }
    }, 5000);
    
    return () => clearInterval(interval);
  }, [eta, distance]);

  // Mock ride details
  const rideDetails = {
    driver: {
      name: "Alex Thompson",
      rating: 4.8,
      phone: "******-123-4567",
      photo: "https://i.pravatar.cc/150?img=12"
    },
    vehicle: {
      type: "sedan",
      model: "Toyota Camry",
      color: "Silver",
      plate: "ABC 1234"
    },
    ride: {
      id: ride_id,  // Using unwrapped ride_id
      pickupTime: "5 minutes",
      fare: "$14.50",
      distance: "5.7 miles",
      duration: "15 minutes",
      pickup: "123 Main St, Brooklyn, NY",
      dropoff: "456 Park Ave, Manhattan, NY"
    },
    otp: "1234" // Usually this would come from an API
  };

  const handleCancelRide = () => {
    if (window.confirm("Are you sure you want to cancel this ride?")) {
      alert("Ride cancelled successfully");
      // Navigate back to home or ride booking page
      window.location.href = `/in/en/ride/${id}`;
    }
  };

  return (
    <div className="h-screen font-[body] flex flex-col bg-white text-black overflow-hidden">
      {/* Header */}
      <header className="bg-black text-white py-4 px-6 flex items-center z-10">
        <Link href={`/in/en/ride/${id}`} className="mr-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </Link>
        <h1 className="text-xl font-bold">Track Your Ride</h1>
      </header>
      
      {/* Map Area */}
      <AnimatePresence>
        <motion.div
          key={isMapExpanded ? 'expanded-map' : 'normal-map'}
          initial={{ opacity: 0.8 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0.8 }}
          transition={{ duration: 0.3 }}
          className={`relative ${isMapExpanded ? 'h-full' : 'h-1/2'} bg-gray-200`}
        >
          {/* Mock map content - replace with actual map integration */}
          <div className="absolute inset-0 bg-gray-200 flex flex-col items-center justify-center">
            <div className="relative w-full h-full">
              {/* Map placeholder - in a real app, integrate with Google Maps or similar */}
              <div className="absolute inset-0 bg-blue-50 opacity-50"></div>
              
              {/* Simulated route line */}
              <svg 
                className="absolute inset-0 w-full h-full" 
                viewBox="0 0 100 100" 
                preserveAspectRatio="none"
              >
                <path 
                  d="M20,80 Q40,40 80,20" 
                  stroke="black" 
                  strokeWidth="0.5" 
                  fill="none" 
                  strokeDasharray="1,1"
                />
              </svg>
              
              {/* Origin point */}
              <div className="absolute bottom-1/4 left-1/5 transform -translate-x-1/2 -translate-y-1/2">
                <div className="h-3 w-3 bg-black rounded-full"></div>
              </div>
              
              {/* Destination point */}
              <div className="absolute top-1/5 right-1/5 transform -translate-x-1/2 -translate-y-1/2">
                <div className="h-3 w-3 bg-black rounded-full border-2 border-white"></div>
              </div>
              
              {/* Moving car icon */}
              <motion.div
                className="absolute"
                animate={{
                  left: `${20 + (80 - 20) * Math.random()}%`,
                  top: `${20 + (80 - 20) * Math.random()}%`,
                }}
                transition={{
                  duration: 5,
                  ease: "linear",
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              >
                <div className="h-6 w-6 bg-black rounded-full flex items-center justify-center text-white text-xs">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h8m0 0l-4-4m4 4l-4 4m-4 6h8m-8 0l4 4m-4-4l4-4" />
                  </svg>
                </div>
              </motion.div>
            </div>
            
            {/* Map controls */}
            <button 
              onClick={() => setIsMapExpanded(!isMapExpanded)}
              className="absolute bottom-4 right-4 bg-white p-3 rounded-full shadow-lg z-10"
            >
              {isMapExpanded ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
                </svg>
              )}
            </button>
          </div>
          
          {/* Bottom overlay with ETA (only when map is not expanded) */}
          {!isMapExpanded && (
            <motion.div 
              className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-4 shadow-lg"
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-lg font-bold">Arriving in {eta}</h3>
                  <p className="text-gray-600">{distance} away</p>
                </div>
                <div className="bg-gray-100 px-4 py-2 rounded-full">
                  <span className="font-medium">OTP: {rideDetails.otp}</span>
                </div>
              </div>
            </motion.div>
          )}
        </motion.div>
      </AnimatePresence>

      {/* Ride details (only visible when map is not expanded) */}
      {!isMapExpanded && (
        <motion.div 
          className="flex-grow overflow-y-auto p-5"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          {/* Driver & Vehicle Info */}
          <div className="bg-white rounded-lg shadow-sm mb-5 p-4">
            <div className="flex items-center space-x-4 mb-4">
              <div className="relative">
                <div className="w-14 h-14 rounded-full overflow-hidden">
                  <img 
                    src={rideDetails.driver.photo} 
                    alt={rideDetails.driver.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="absolute -bottom-1 -right-1 bg-green-500 w-4 h-4 rounded-full border-2 border-white"></div>
              </div>
              <div>
                <h2 className="font-bold">{rideDetails.driver.name}</h2>
                <div className="flex items-center space-x-1">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                  <span className="text-sm">{rideDetails.driver.rating}</span>
                </div>
              </div>
              <div className="ml-auto flex space-x-2">
                <motion.button 
                  className="p-2 bg-gray-100 rounded-full"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                </motion.button>
                <motion.button 
                  className="p-2 bg-gray-100 rounded-full"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </motion.button>
              </div>
            </div>
            
            {/* Vehicle Info */}
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gray-200 rounded-full">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h8m0 0l-4-4m4 4l-4 4m-4 6h8m-8 0l4 4m-4-4l4-4" />
                  </svg>
                </div>
                <div>
                  <p className="font-medium">{rideDetails.vehicle.model}</p>
                  <p className="text-sm text-gray-600">{rideDetails.vehicle.color}</p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-bold">{rideDetails.vehicle.plate}</p>
                <p className="text-sm text-gray-600 uppercase">{rideDetails.vehicle.type}</p>
              </div>
            </div>
          </div>
          
          {/* Route Details */}
          <div className="mb-5">
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-start mb-4">
                <div className="min-w-[24px] h-full flex flex-col items-center">
                  <div className="w-3 h-3 rounded-full bg-black"></div>
                  <div className="w-0.5 h-12 bg-gray-300 my-1"></div>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-gray-500">PICKUP</p>
                  <p className="font-medium">{rideDetails.ride.pickup}</p>
                </div>
              </div>
              <div className="flex items-start">
                <div className="min-w-[24px] h-6 flex items-center justify-center">
                  <div className="w-3 h-3 rounded-full border-2 border-black bg-white"></div>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-gray-500">DROPOFF</p>
                  <p className="font-medium">{rideDetails.ride.dropoff}</p>
                </div>
              </div>
            </div>
          </div>
          
          {/* OTP Verification */}
          <div className="bg-white rounded-lg shadow-sm mb-5 p-4">
            <h3 className="font-bold mb-3">Verify OTP</h3>
            <p className="text-sm text-gray-600 mb-3">Share this OTP with your driver to confirm your ride</p>
            
            <div className="flex justify-center items-center p-3 bg-gray-100 rounded-lg mb-2">
              <div className="text-3xl font-bold tracking-wider">{rideDetails.otp}</div>
            </div>
            
            <p className="text-xs text-center text-gray-500">
              Your OTP ensures you get into the right vehicle
            </p>
          </div>
        </motion.div>
      )}
      
      {/* Footer with cancel option (only when map is not expanded) */}
      {!isMapExpanded && (
        <div className="p-4 border-t border-gray-200 bg-white">
          <motion.button
            className="w-full py-3 px-4 bg-black text-white rounded-md font-medium hover:bg-gray-900 transition-colors"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleCancelRide}
          >
            Cancel Ride
          </motion.button>
        </div>
      )}
    </div>
  );
}
