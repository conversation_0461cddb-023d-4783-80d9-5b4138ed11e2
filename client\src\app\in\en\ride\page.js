'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { FaMapMarkerAlt, FaLocationArrow, FaSearch, FaBars } from 'react-icons/fa';

export default function RidePage() {
  const router = useRouter();
  const [pickup, setPickup] = useState('');
  const [destination, setDestination] = useState('');
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    // Here you would implement the ride request logic
    console.log('Ride requested from', pickup, 'to', destination);
    // You could redirect to a ride confirmation page or show a modal
  };

  return (
    <div className="min-h-screen bg-gray-50 text-black font-[body]">
      {/* Header */}
      <header className="bg-black text-white p-4 flex justify-between items-center">
        <div className="text-2xl font-bold font-[head]">RideUp</div>
        
        {/* Desktop Navigation */}
        <nav className="hidden md:block">
          <ul className="flex space-x-6">
            <li className="cursor-pointer hover:text-gray-300">Ride</li>
            <li className="cursor-pointer hover:text-gray-300">Drive</li>
            <li className="cursor-pointer hover:text-gray-300">Profile</li>
          </ul>
        </nav>
        
        {/* Login/Signup */}
        <div className="flex items-center space-x-4">
          <button onClick={() => router.push('/user/login')} className="px-4 py-1 border border-white rounded hover:bg-white hover:text-black transition">Login</button>
          <button onClick={() => router.push('/user/register')} className="px-4 py-1 bg-white text-black rounded hover:bg-gray-200 transition">Signup</button>
          
          {/* Mobile Navigation Icon */}
          <button className="md:hidden text-xl" onClick={() => setMobileMenuOpen(!mobileMenuOpen)}>
            <FaBars />
          </button>
        </div>
      </header>
      
      {/* Mobile Navigation Menu - Shown when mobile menu is open */}
      {mobileMenuOpen && (
        <div className="md:hidden bg-black absolute w-[100%] flex flex-col justify-center items-center text-white p-4">
          <ul className="space-y-3">
            <li className="cursor-pointer hover:text-gray-300">Ride</li>
            <li className="cursor-pointer hover:text-gray-300">Drive</li>
            <li className="cursor-pointer hover:text-gray-300">Profile</li>
          </ul>
        </div>
      )}

      <main className="container mx-auto p-4 flex flex-col md:flex-row gap-6">
        {/* Left side - Ride request form */}
        <div className="w-full md:w-1/3 bg-white p-6 rounded-lg shadow-lg">
          <h1 className="text-2xl font-bold font-[bodyitalic] mb-6">Request a ride for now or later</h1>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="relative">
              <FaLocationArrow className="absolute left-3 top-3 text-gray-400" />
              <input
                type="text"
                placeholder="Enter pickup location"
                className="w-full pl-10 p-2 border border-gray-300 rounded"
                value={pickup}
                onChange={(e) => setPickup(e.target.value)}
                required
              />
            </div>

            <div className="relative">
              <FaMapMarkerAlt className="absolute left-3 top-3 text-gray-400" />
              <input
                type="text"
                placeholder="Enter destination"
                className="w-full pl-10 p-2 border border-gray-300 rounded"
                value={destination}
                onChange={(e) => setDestination(e.target.value)}
                required
              />
            </div>

            <button
              type="submit"
              className="w-full bg-black text-white py-2 rounded font-medium hover:bg-gray-800 transition"
            >
              Request Ride
            </button>
          </form>
        </div>

        {/* Right side - Map placeholder */}
        <div className="w-full md:w-2/3 bg-gray-200 rounded-lg min-h-[400px] flex items-center justify-center">
          <div className="text-center">
            <FaSearch className="mx-auto text-4xl text-gray-400 mb-2" />
            <p className="text-gray-600">Map will be displayed here</p>
            <p className="text-sm text-gray-500">Integrate with a mapping API for real functionality</p>
          </div>
        </div>
      </main>
    </div>
  );
}
