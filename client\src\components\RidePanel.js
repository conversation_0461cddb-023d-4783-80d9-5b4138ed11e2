import { useState, useRef } from 'react';
import { motion, useDragControls, AnimatePresence } from 'framer-motion';
import { MapPinIcon, ArrowRightIcon, ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/solid';
import LocationInput from './LocationInput';
import VehicleSelection from './VehicleSelection';
import WaitingDriver from './WaitingDriver';
import ConfirmedRide from './ConfirmedRide';
import RazorpayCheckout from './RazorpayCheckout';

export default function RidePanel({ 
  isMobile, 
  isPanelExpanded, 
  setIsPanelExpanded,
  location,
  setLocation,
  destination,
  setDestination,
  isVehicleSelectionMode,
  setIsVehicleSelectionMode,
  rideId
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const [activeInput, setActiveInput] = useState(null);
  const [selectedVehicleType, setSelectedVehicleType] = useState(null);
  const [rideState, setRideState] = useState('input'); // 'input', 'selection', 'payment', 'waiting', 'confirmed'
  const [paymentMethod, setPaymentMethod] = useState(null);
  const [isPaymentComplete, setIsPaymentComplete] = useState(false);
  const [rideDetails, setRideDetails] = useState(null);
  
  const dragControls = useDragControls();
  
  // Mock function for location suggestions
  const handleInputChange = (value, type) => {
    if (type === 'location') {
      setLocation(value);
    } else {
      setDestination(value);
    }
    
    if (value.length > 2) {
      setIsLoading(true);
      // Simulate API call delay
      setTimeout(() => {
        setSuggestions([
          `${value} Street`,
          `${value} Avenue`,
          `${value} Boulevard`,
          `${value} Plaza`
        ]);
        setIsLoading(false);
      }, 500);
    } else {
      setSuggestions([]);
    }
  };

  const handleSuggestionClick = (suggestion) => {
    if (activeInput === 'location') {
      setLocation(suggestion);
    } else {
      setDestination(suggestion);
    }
    setSuggestions([]);
    setActiveInput(null);
  };

  const handleFindDrivers = (e) => {
    e.preventDefault();
    setIsVehicleSelectionMode(true);
    setRideState('selection');
  };

  const handleConfirmRide = (selectedPaymentMethod) => {
    setPaymentMethod(selectedPaymentMethod);
    
    if (selectedPaymentMethod === 'cash') {
      // Directly go to waiting screen for cash payments
      setRideState('waiting');
      simulateDriverConfirmation();
    } else {
      // Go to online payment flow
      setRideState('payment');
    }
  };

  const handlePaymentComplete = (paymentInfo) => {
    setIsPaymentComplete(true);
    setRideState('waiting');
    simulateDriverConfirmation();
  };

  const handleBackToLocationSelection = () => {
    setIsVehicleSelectionMode(false);
    setRideState('input');
    setSelectedVehicleType(null);
  };

  // Simulate driver confirmation after a delay
  const simulateDriverConfirmation = () => {
    setTimeout(() => {
      setRideDetails({
        driver: {
          name: "Alex Thompson",
          rating: 4.8,
          phone: "******-123-4567",
          photo: "https://i.pravatar.cc/150?img=12"
        },
        vehicle: {
          type: selectedVehicleType,
          model: "Toyota Camry",
          color: "Silver",
          plate: "ABC 1234"
        },
        ride: {
          id: rideId,
          pickupTime: "5 minutes",
          fare: paymentMethod === 'cash' ? "Pay $14.50 in cash" : "Paid $14.50 online",
          distance: "5.7 miles",
          duration: "15 minutes"
        }
      });
      setRideState('confirmed');
    }, 5000); // 5 seconds delay
  };
  
  // Animation variants
  const panelVariants = {
    collapsed: {
      height: isMobile ? "35vh" : "auto",
      y: 0
    },
    expanded: {
      height: "85vh",
      y: 0
    }
  };

  // Content transition variants
  const contentVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: { duration: 0.3 }
    },
    exit: { 
      opacity: 0, 
      x: 20,
      transition: { duration: 0.2 }
    }
  };

  const renderContent = () => {
    switch (rideState) {
      case 'input':
        return (
          <>
            <h2 className="text-2xl font-bold text-black mb-6">Plan Your Ride</h2>
            
            <form onSubmit={handleFindDrivers} className="space-y-6">
              <LocationInput
                type="location"
                label="Pickup Location"
                placeholder="Enter pickup location"
                value={location}
                onChange={(e) => handleInputChange(e.target.value, 'location')}
                onFocus={() => setActiveInput('location')}
                isActive={activeInput === 'location'}
                isLoading={isLoading}
                suggestions={suggestions}
                onSuggestionClick={handleSuggestionClick}
              />
              
              <div className="flex justify-center">
                <motion.div 
                  className="bg-gray-100 p-2 rounded-full"
                  animate={{ y: [0, -5, 0] }}
                  transition={{ repeat: Infinity, duration: 1.5 }}
                >
                  <ArrowRightIcon className="h-6 w-6 text-black rotate-90" />
                </motion.div>
              </div>
              
              <LocationInput
                type="destination"
                label="Drop-off Location"
                placeholder="Enter destination"
                value={destination}
                onChange={(e) => handleInputChange(e.target.value, 'destination')}
                onFocus={() => setActiveInput('destination')}
                isActive={activeInput === 'destination'}
                isLoading={isLoading}
                suggestions={suggestions}
                onSuggestionClick={handleSuggestionClick}
              />
              
              <motion.div 
                className="pt-4"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <button
                  type="submit"
                  disabled={!location || !destination}
                  className={`w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-lg font-medium text-white 
                    ${(!location || !destination) ? 'bg-gray-300' : 'bg-black hover:bg-gray-800'} 
                    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200`}
                >
                  Find Drivers
                </button>
              </motion.div>
            </form>
          </>
        );
      
      case 'selection':
        return (
          <>
            <motion.button
              className="mb-4 flex items-center text-gray-600 hover:text-black"
              onClick={handleBackToLocationSelection}
              initial={{ x: -10, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              whileHover={{ x: -3 }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
              </svg>
              Change Route
            </motion.button>
            
            <h2 className="text-2xl font-bold text-black mb-6">Choose Your Ride</h2>
            
            {/* Location summary */}
            <div className="mb-6 bg-gray-50 p-4 rounded-lg">
              <div className="flex items-start mb-3">
                <div className="min-w-[24px] h-6 flex items-center justify-center">
                  <div className="w-3 h-3 rounded-full bg-black"></div>
                </div>
                <div className="ml-2">
                  <p className="text-sm text-gray-500">From</p>
                  <p className="font-medium">{location}</p>
                </div>
              </div>
              <div className="flex items-start">
                <div className="min-w-[24px] h-6 flex items-center justify-center">
                  <div className="w-3 h-3 rounded-full bg-black border-2 border-white shadow"></div>
                </div>
                <div className="ml-2">
                  <p className="text-sm text-gray-500">To</p>
                  <p className="font-medium">{destination}</p>
                </div>
              </div>
            </div>
            
            <VehicleSelection
              selectedVehicleType={selectedVehicleType}
              setSelectedVehicleType={setSelectedVehicleType}
              onConfirmRide={handleConfirmRide}
            />
          </>
        );
      
      case 'payment':
        return (
          <RazorpayCheckout 
            amount={1450}  // $14.50 in cents
            onPaymentComplete={handlePaymentComplete}
            onCancel={() => setRideState('selection')}
            rideDetails={{
              from: location,
              to: destination,
              vehicleType: selectedVehicleType
            }}
          />
        );
      
      case 'waiting':
        return (
          <WaitingDriver 
            location={location}
            destination={destination}
            vehicleType={selectedVehicleType}
            paymentMethod={paymentMethod}
          />
        );
      
      case 'confirmed':
        return (
          <ConfirmedRide 
            rideDetails={rideDetails}
          />
        );
        
      default:
        return null;
    }
  };

  const isFullscreenMode = rideState === 'confirmed';

  return (
    <motion.div
      className={`${
        isFullscreenMode 
          ? "fixed inset-0 z-50 bg-white font-[body]" 
          : "absolute bottom-0 left-0 right-0 font-[body] bg-white rounded-t-3xl shadow-xl md:relative md:order-1 md:w-2/5 md:h-auto md:rounded-none"
      }`}
      variants={!isFullscreenMode ? panelVariants : {}}
      initial={isFullscreenMode ? { opacity: 0 } : "collapsed"}
      animate={
        isFullscreenMode 
          ? { opacity: 1 } 
          : (isPanelExpanded ? "expanded" : "collapsed")
      }
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
      drag={isMobile && !isFullscreenMode ? "y" : false}
      dragControls={dragControls}
      dragConstraints={{ top: 0, bottom: 0 }}
      dragElastic={0.2}
      dragMomentum={false}
      onDragEnd={(e, { offset, velocity }) => {
        if (offset.y < -50 || velocity.y < -500) {
          setIsPanelExpanded(true);
        } else if (offset.y > 50 || velocity.y > 500) {
          setIsPanelExpanded(false);
        }
      }}
    >
      {/* Handle for drag on mobile (only when not in fullscreen) */}
      {isMobile && !isFullscreenMode && (
        <div 
          className="w-full flex justify-center py-2 cursor-grab active:cursor-grabbing"
          onPointerDown={(e) => dragControls.start(e)}
        >
          <div className="w-10 h-1 bg-gray-300 rounded-full"></div>
        </div>
      )}

      {/* Expand/Collapse button on mobile (only when not in fullscreen) */}
      {isMobile && !isFullscreenMode && (
        <button 
          className="absolute right-4 top-4 p-2" 
          onClick={() => setIsPanelExpanded(!isPanelExpanded)}
        >
          {isPanelExpanded ? 
            <ChevronDownIcon className="h-5 w-5 text-gray-600" /> : 
            <ChevronUpIcon className="h-5 w-5 text-gray-600" />
          }
        </button>
      )}
      
      {/* Form Content */}
      <div className={`${isFullscreenMode ? 'h-full' : 'p-6 h-full overflow-y-auto'}`}>
        <AnimatePresence mode="wait">
          <motion.div
            key={rideState}
            variants={contentVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className={isFullscreenMode ? 'h-full' : ''}
          >
            {renderContent()}
          </motion.div>
        </AnimatePresence>
        
        {(!isVehicleSelectionMode && isPanelExpanded && rideState === 'input') && (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mt-8 space-y-4"
          >
            <div className="border-t border-gray-200 pt-4 text-sm text-gray-500 flex justify-between">
              <p>Ride ID: {rideId}</p>
              <p>Need help? <span className="text-black underline cursor-pointer hover:no-underline">Contact support</span></p>
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
}
