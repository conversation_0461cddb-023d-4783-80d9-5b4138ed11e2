import { motion } from 'framer-motion';
import { MapPinIcon } from '@heroicons/react/24/solid';

export default function LocationInput({
  type,
  label,
  placeholder,
  value,
  onChange,
  onFocus,
  isActive,
  isLoading,
  suggestions,
  onSuggestionClick
}) {
  return (
    <div className="relative font-[body]">
      <label className="block text-sm font-medium text-gray-700 mb-2">{label}</label>
      <div className="mt-1 relative rounded-md shadow-sm">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MapPinIcon className="h-5 w-5 text-black" aria-hidden="true" />
        </div>
        <input
          type="text"
          value={value}
          onChange={onChange}
          onFocus={onFocus}
          className="focus:ring-black focus:border-black block w-full pl-10 pr-12 sm:text-sm border-gray-300 rounded-md py-3"
          placeholder={placeholder}
        />
      </div>
      {isActive && suggestions.length > 0 && (
        <motion.div 
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md py-1"
        >
          {isLoading ? (
            <div className="p-4 text-center">
              <div className="animate-pulse text-gray-500">Loading suggestions...</div>
            </div>
          ) : (
            suggestions.map((suggestion, index) => (
              <motion.div
                key={index}
                className="px-4 py-2 hover:bg-gray-50 cursor-pointer"
                onClick={() => onSuggestionClick(suggestion)}
                whileHover={{ backgroundColor: "#f9fafb" }}
              >
                {suggestion}
              </motion.div>
            ))
          )}
        </motion.div>
      )}
    </div>
  );
}
