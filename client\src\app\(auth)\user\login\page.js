'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import AuthLayout from '@/components/auth/AuthLayout';
import EmailLoginForm from '@/components/auth/EmailLoginForm';
import PhoneLoginForm from '@/components/auth/PhoneLoginForm';
import SocialLogin from '@/components/auth/SocialLogin';
import LoginTabs from '@/components/auth/LoginTabs';
import ErrorMessage from '@/components/auth/ErrorMessage';

export default function LoginPage() {
  const [activeTab, setActiveTab] = useState('email');
  const [localError, setLocalError] = useState('');
  
  const { loginUserAccount, loading, error: contextError } = useAuth();

  const handleEmailLogin = async ({ email, password }) => {
    setLocalError('');
    
    if (!email || !password) {
      setLocalError('Please fill in all fields');
      return;
    }
    
    try {
      const result = await loginUserAccount({ email, password });
      
      if (!result.success) {
        setLocalError(result.error);
      }
    } catch (err) {
      setLocalError('Login failed. Please check your credentials.');
      console.error(err);
    }
  };

  const handlePhoneLogin = async ({ phone }) => {
    setLocalError('');
    
    if (!phone) {
      setLocalError('Please enter your phone number');
      return;
    }
    
    setLocalError('Phone login is not yet implemented');
  };

  const handleGoogleLogin = async () => {
    setLocalError('Google login is not yet implemented');
  };

  const signupLink = (
    <>
      Don't have an account?{' '}
      <Link href="/user/register" className="font-medium text-black hover:text-gray-800">
        Sign up
      </Link>
    </>
  );

  return (
    <AuthLayout 
      title="Welcome back" 
      subtitle="Log in to your RideUp account"
      footer={signupLink}
    >
      <LoginTabs activeTab={activeTab} onTabChange={setActiveTab} />
      
      <ErrorMessage message={localError || contextError} />
      
      {activeTab === 'email' && (
        <EmailLoginForm onSubmit={handleEmailLogin} loading={loading} />
      )}
      
      {activeTab === 'phone' && (
        <PhoneLoginForm onSubmit={handlePhoneLogin} loading={loading} />
      )}
      
      <SocialLogin onGoogleLogin={handleGoogleLogin} loading={loading} />
    </AuthLayout>
  );
}
